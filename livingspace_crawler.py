#!/usr/bin/env python3
import time
import os
import datetime
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here
URL = "https://americanhomefurniture.com/collections/dining-chairs"

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        # Add these options for compatibility
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        chrome_version = 135  # Update this to match your Chrome version
        driver = uc.Chrome(version_main=chrome_version, options=options)
        
        driver.get(url)
        
        input("Please solve the CAPTCHA in the browser if needed, then press Enter here to continue...")
        
        # Try to close any overlays/popups that might appear
        close_overlays(driver)
        
        # Create output directory if it doesn't exist
        output_dir = 'livingspace'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"livingspaces_{timestamp}.txt")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Living Spaces - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing product titles: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        
        while current_page <= max_pages and not last_page_reached:
            print(f"\n==== Scraping page {current_page} ====")
            page_results = process_page(driver.page_source)
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Use Selenium to click the next pagination arrow if present
            try:
                next_arrow = None
                try:
                    next_arrow = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'div.pagination-arrow:not(.pagination-arrow-disabled) .fa-angle-right'))
                    )
                except TimeoutException:
                    print("No more pagination arrows found. Reached the last page.")
                    last_page_reached = True
                    break
                if next_arrow:
                    print("Clicking next pagination arrow...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_arrow)
                    time.sleep(1)
                    try:
                        next_arrow.click()
                    except ElementClickInterceptedException:
                        print("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_arrow)
                    time.sleep(3)
                    current_page += 1
                else:
                    print("No next pagination arrow found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                print(f"Error during pagination: {e}")
                last_page_reached = True
                break
                
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All product titles have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nSample Product Titles:")
            for i, title in enumerate(all_results[:10], 1):
                print(f"{i}. {title}")
            if len(all_results) > 10:
                print(f"... and {len(all_results) - 10} more")
            print(f"\nTotal products found: {len(all_results)}")
        input("\nPress Enter to close the browser and finish the script...")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver and input("Do you want to keep the browser open? (y/n): ").lower() != 'y':
            driver.quit()

def close_overlays(driver):
    close_buttons = driver.find_elements(By.XPATH, '//div[@aria-label="Close"] | //button[@aria-label="Close"]')
    if close_buttons:
        print(f"Found {len(close_buttons)} potential overlay(s). Attempting to close...")
        for button in close_buttons:
            try:
                button.click()
                print("Clicked a close button.")
                time.sleep(1)
            except Exception as e:
                print(f"Could not click close button: {e}")
                try:
                    driver.execute_script("arguments[0].click();", button)
                    print("Used JavaScript to click close button.")
                    time.sleep(1)
                except Exception as js_e:
                    print(f"JavaScript click also failed: {js_e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    product_containers = soup.find_all('div', class_='product-item-container')
    for container in product_containers:
        name_tag = container.find('h3', class_='name')
        if name_tag:
            title_text = name_tag.get_text(strip=True)
            if title_text:
                results.append(title_text)
    if not results:
        print("No product titles found with <div class='product-item-container'> selector.")
    return results

def main():
    print("Living Spaces Product Title Crawler")
    custom_url = input(f"Enter Living Spaces URL or press Enter to use default [{URL}]: ")
    if not custom_url.strip():
        custom_url = URL
    
    max_pages = input("Enter maximum number of pages to crawl (default: 10): ")
    try:
        max_pages = int(max_pages)
    except (ValueError, TypeError):
        max_pages = 10
    
    print(f"\nStarting crawler for: {custom_url}")
    print(f"Will crawl up to {max_pages} pages\n")
    
    # Ensure livingspace directory exists
    os.makedirs('livingspace', exist_ok=True)
    print(f"Livingspace directory created/verified at: {os.path.abspath('livingspace')}")
    
    selenium_crawl(custom_url, max_pages=max_pages)

if __name__ == "__main__":
    main() 