#!/usr/bin/env python3
import time
import os
import datetime
import random
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import re

# Set your starting URL here
URL = "https://gardenartisans.com/garden-decor-art/"

def crawl_all_categories(start_url, max_depth=5):
    """
    Main function to crawl through all categories and subcategories
    """
    driver = None
    try:
        print("Starting autonomous crawler for Garden Artisans categories")
        
        # Setup driver
        options = uc.ChromeOptions()
        # --- Anti-bot evasion settings ---
        user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/123.0.0.0 Safari/537.36"
        )
        options.add_argument(f'user-agent={user_agent}')
        options.add_argument(f'--user-data-dir=/tmp/chrome_profile_{random.randint(10000,99999)}')
        options.add_argument('window-size=1200,900')
        options.add_argument('--lang=en-US,en')
        options.add_argument('--disable-blink-features=AutomationControlled')
        # Do NOT use headless mode
        # --- End anti-bot evasion settings ---
        chrome_version = 135  # Update this to match your Chrome version
        driver = uc.Chrome(version_main=chrome_version, options=options)
        
        # Create output directory and file
        output_dir = 'gardenartisan'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"gardenartisan_{timestamp}.txt")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Garden Artisans - Product Titles\n")
            f.write(f"# Starting URL: {start_url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing product titles: {os.path.abspath(filename)}")
        
        # Visit the start URL first to extract navigation structure
        driver.get(start_url)
        time.sleep(random.uniform(2, 4))
        
        # Accept cookies if the dialog appears
        try:
            cookie_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Accept")]'))
            )
            cookie_button.click()
            print("Accepted cookies")
            time.sleep(random.uniform(1, 2))
        except TimeoutException:
            print("No cookie dialog found or it was already accepted")
        
        # Allow some time for the page to fully load
        print("Waiting for page to load completely...")
        time.sleep(5)
        
        # Try to close any overlays/popups that might appear
        close_overlays(driver)
        time.sleep(random.uniform(1, 2))
        
        # Get all category links
        all_categories = extract_category_links(driver)
        print(f"Found {len(all_categories)} categories to process")
        
        # Process the current URL first (it might not be in the navigation)
        print(f"\n==== Starting with current page: {start_url} ====")
        process_category_page(driver, start_url, filename)
        
        # Now process each category systematically
        visited_urls = set([start_url])
        
        for category_url in all_categories:
            if category_url in visited_urls:
                print(f"Skipping already visited category: {category_url}")
                continue
            
            visited_urls.add(category_url)
            print(f"\n==== Processing category: {category_url} ====")
            
            try:
                # Navigate to the category page
                driver.get(category_url)
                time.sleep(random.uniform(2, 4))
                
                # Close any popups
                close_overlays(driver)
                time.sleep(random.uniform(1, 2))
                
                # Process this category page
                process_category_page(driver, category_url, filename)
                
                # After processing, get any subcategories we need to process
                subcategories = extract_subcategory_links(driver, category_url)
                for subcategory in subcategories:
                    if subcategory not in visited_urls:
                        all_categories.append(subcategory)
                
            except Exception as e:
                print(f"Error processing category {category_url}: {e}")
        
        print("\n==== Autonomous crawling complete ====")
        print(f"All product titles have been saved to: {os.path.abspath(filename)}")
        
        return filename
    
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return None
    finally:
        if driver:
            print("Closing browser...")
            driver.quit()

def extract_category_links(driver):
    """Extract all category links from the navigation menu"""
    try:
        # Find the navigation tree
        nav_tree = driver.find_element(By.CSS_SELECTOR, "ul.navigation-treeview")
        
        # Get all links from the navigation
        links = nav_tree.find_elements(By.TAG_NAME, "a")
        
        # Extract the URLs and filter out any duplicates
        category_urls = []
        for link in links:
            url = link.get_attribute('href')
            if url and url not in category_urls:
                # Skip links that don't go to product pages (FAQ, blog, etc.)
                if not any(skip in url for skip in ['catalog-request', 'faqs', 'blog']):
                    category_urls.append(url)
        
        print(f"Extracted {len(category_urls)} category links from navigation")
        return category_urls
    
    except Exception as e:
        print(f"Error extracting category links: {e}")
        return []

def extract_subcategory_links(driver, parent_url):
    """Extract subcategory links from a category page"""
    try:
        # Look for subcategory navigation
        subcategories = []
        nav_items = driver.find_elements(By.CSS_SELECTOR, "ul.navigation-treeview li.navList-item")
        
        for item in nav_items:
            # Check if this is a subcategory of the current page
            try:
                link = item.find_element(By.TAG_NAME, "a")
                url = link.get_attribute('href')
                
                # Only include if it's a subcategory of the current category
                parent_path = '/'.join(parent_url.split('/')[:-2])  # Remove trailing slash and last path segment
                if url and url.startswith(parent_path) and url != parent_url:
                    subcategories.append(url)
            except NoSuchElementException:
                continue
        
        print(f"Found {len(subcategories)} subcategories")
        return subcategories
    
    except Exception as e:
        print(f"Error extracting subcategory links: {e}")
        return []

def process_category_page(driver, category_url, output_file, max_pages=10):
    """Process a single category page, including all its pagination"""
    print(f"Processing products on {category_url}")
    
    # Get category name from URL for logging
    category_name = category_url.rstrip('/').split('/')[-1]
    category_name = category_name.replace('-', ' ').title()
    
    all_results = []
    current_page = 1
    last_page_reached = False
    
    # Write category header to file
    with open(output_file, 'a', encoding='utf-8') as f:
        f.write(f"\n# Category: {category_name}\n")
    
    while current_page <= max_pages and not last_page_reached:
        print(f"\n==== Scraping {category_name} page {current_page} ====")
        
        # Scroll down slowly to load all products
        for i in range(10):
            driver.execute_script(f"window.scrollTo(0, {i * 500});")
            time.sleep(random.uniform(0.2, 0.5))
        
        # Process the current page
        page_results = process_page(driver.page_source)
        time.sleep(random.uniform(1, 2))
        
        if page_results:
            all_results.extend(page_results)
            print(f"Found {len(page_results)} products on page {current_page}")
            with open(output_file, 'a', encoding='utf-8') as f:
                for title in page_results:
                    f.write(f"{title}\n")
            
            for i, title in enumerate(page_results[:5], 1):
                print(f"  {i}. {title}")
            if len(page_results) > 5:
                print(f"  ... and {len(page_results) - 5} more")
            print(f"  Appended {len(page_results)} products to {output_file}")
        else:
            print(f"No products found on page {current_page}")
        
        if current_page >= max_pages:
            print(f"Reached maximum page limit ({max_pages})")
            break
        
        # Use Selenium to click the next pagination link if present
        try:
            next_button = None
            try:
                # Using the Garden Artisans pagination structure provided
                next_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'li.pagination-item--next a.pagination-link'))
                )
            except TimeoutException:
                # Try alternative selector
                try:
                    next_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, '//a[contains(text(), "Next")]'))
                    )
                except TimeoutException:
                    print("No next page button found. Reached the last page.")
                    last_page_reached = True
                    break
            
            if next_button:
                print("Clicking next pagination button...")
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                time.sleep(random.uniform(1, 2))
                try:
                    next_button.click()
                except ElementClickInterceptedException:
                    print("Direct click failed, trying JavaScript click...")
                    driver.execute_script("arguments[0].click();", next_button)
                
                # Wait longer after pagination to ensure the new page loads
                print("Waiting for next page to load...")
                time.sleep(random.uniform(3, 5))
                
                # Scroll back to top of page
                driver.execute_script("window.scrollTo(0, 0);")
                
                # Additional wait for elements to load after scrolling back up
                time.sleep(1)
                
                current_page += 1
            else:
                print("No next pagination button found. Reached the last page.")
                last_page_reached = True
                break
        except Exception as e:
            print(f"Error during pagination: {e}")
            last_page_reached = True
            break
    
    print(f"Completed {category_name} with {len(all_results)} products across {current_page} pages")
    return all_results

def close_overlays(driver):
    # Try to close various types of overlays common on e-commerce sites
    selectors = [
        '//button[@aria-label="Close"]',
        '//div[@aria-label="Close"]',
        '//button[contains(text(), "Close")]',
        '//button[contains(@class, "close")]',
        '//span[contains(@class, "close")]',
        '//div[contains(@class, "popup-close")]',
        '//button[contains(text(), "No thanks")]',
        '//a[contains(text(), "Close")]'
    ]
    
    for selector in selectors:
        try:
            close_buttons = driver.find_elements(By.XPATH, selector)
            if close_buttons:
                print(f"Found {len(close_buttons)} potential overlay(s) with selector {selector}. Attempting to close...")
                for button in close_buttons:
                    try:
                        button.click()
                        print("Clicked a close button.")
                        time.sleep(random.uniform(0.5, 1))
                    except Exception as e:
                        print(f"Could not click close button: {e}")
                        try:
                            driver.execute_script("arguments[0].click();", button)
                            print("Used JavaScript to click close button.")
                            time.sleep(random.uniform(0.5, 1))
                        except Exception as js_e:
                            print(f"JavaScript click also failed: {js_e}")
        except Exception as e:
            print(f"Error finding overlay close buttons with selector {selector}: {e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    
    # Look for products with the exact selector for Garden Artisans
    # Based on the provided HTML structure, targeting h2.card-title a elements
    product_titles = soup.find_all('h2', class_='card-title')
    
    if product_titles:
        print(f"Found {len(product_titles)} product titles using h2.card-title selector")
        for title_elem in product_titles:
            try:
                # Extract the link inside the h2.card-title element
                link = title_elem.find('a')
                if link:
                    # Get the product title text and normalize whitespace
                    title_text = link.get_text(strip=True)
                    # Replace multiple spaces and newlines with a single space
                    title = ' '.join(title_text.split())
                    
                    # Remove " - Set Of 2" from the title
                    title = title.replace(" - Set Of 2", "")
                    
                    if title and title not in results:  # Avoid duplicates
                        results.append(title)
            except Exception as e:
                print(f"Error extracting product info: {e}")
        
        return results
    
    # If no product titles were found, try alternative selectors
    print("No products found with h2.card-title selector. Trying alternative selectors...")
    
    # Try direct link approach
    links = soup.find_all('a', attrs={'data-event-type': 'product-click'})
    if links:
        print(f"Found {len(links)} products using a[data-event-type='product-click'] selector")
        for link in links:
            try:
                # Get the product title text and normalize whitespace
                title_text = link.get_text(strip=True)
                # Replace multiple spaces and newlines with a single space
                title = ' '.join(title_text.split())
                
                # Remove " - Set Of 2" from the title
                title = title.replace(" - Set Of 2", "")
                
                if title and title not in results:  # Avoid duplicates
                    results.append(title)
            except Exception as e:
                print(f"Error extracting product info: {e}")
        
        return results
    
    # Try other alternative selectors if needed
    alternative_selectors = [
        ('div', 'listview-cardbody'),
        ('a', 'product-link'),
        ('a', lambda c: c and 'product-click' in c)
    ]
    
    for tag, class_name in alternative_selectors:
        elements = soup.find_all(tag, class_=class_name)
        if elements:
            print(f"Found {len(elements)} products using {tag}.{class_name} selector")
            for elem in elements:
                try:
                    # Look for product title in different ways depending on the element
                    if tag == 'div':  # For listview-cardbody
                        title_elem = elem.find('h2', class_='card-title')
                        if title_elem and title_elem.find('a'):
                            title_text = title_elem.find('a').get_text(strip=True)
                        else:
                            continue
                    else:  # For direct links
                        title_text = elem.get_text(strip=True)
                    
                    # Replace multiple spaces and newlines with a single space
                    title = ' '.join(title_text.split())
                    
                    # Remove " - Set Of 2" from the title
                    title = title.replace(" - Set Of 2", "")
                    
                    if title and title not in results:  # Avoid duplicates
                        results.append(title)
                except Exception as e:
                    print(f"Error extracting product info: {e}")
            break
    
    if not results:
        print("No products found with any of the selectors. The site structure might have changed.")
        print("Dumping some HTML to help debug:")
        # Print a small section of the HTML to help debug
        print(soup.prettify()[:1000])
    
    return results

def main():
    print("Garden Artisans Autonomous Product Crawler")
    start_url = URL
    
    print(f"\nStarting crawler with initial URL: {start_url}")
    print("The crawler will automatically navigate through all categories and subcategories\n")
    
    # Ensure output directory exists
    os.makedirs('gardenartisan', exist_ok=True)
    
    # Start the autonomous crawling
    crawl_all_categories(start_url)

if __name__ == "__main__":
    main() 