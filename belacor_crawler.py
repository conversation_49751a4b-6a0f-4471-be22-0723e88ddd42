import time
import os
import datetime
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here (example: Pendant Lights category)
URL = "https://www.1800lighting.com/lamps-table-lamp-desk-task-lamp/?utm_medium=referral&utm_source=bellacor"

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        driver = uc.Chrome(options=options)
        driver.get(url)
        
        input("Please solve the CAPTCHA in the browser, then press Enter here to continue...")
        
        # Create output directory if it doesn't exist
        output_dir = 'belacor'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"belacor_{timestamp}.txt")
        
        # Create file with header at the beginning
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Belacor - Keywords\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing keywords: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        
        while current_page <= max_pages and not last_page_reached:
            print(f"\n==== Scraping page {current_page} ====")
            page_results = process_page(driver.page_source)
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Find the next page link using BeautifulSoup
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            next_page_link = None
            for a in soup.find_all('a', class_=lambda x: x and 'cl-pagination__link' in x):
                if a.get_text(strip=True) == str(current_page + 1):
                    next_page_link = a.get('href')
                    break
            if next_page_link:
                print(f"Navigating to next page: {next_page_link}")
                driver.get(next_page_link)
                time.sleep(3)
                current_page += 1
            else:
                print("No more pagination links found. Reached the last page.")
                last_page_reached = True
                break
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All keywords have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nAll Product Titles:")
            for i, title in enumerate(all_results, 1):
                print(f"{i}. {title}")
            print(f"\nTotal products found: {len(all_results)}")
        input("\nPress Enter to close the browser and finish the script...")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver and input("Do you want to keep the browser open? (y/n): ").lower() != 'y':
            driver.quit()

def close_overlays(driver):
    close_buttons = driver.find_elements(By.XPATH, '//div[@aria-label="Close"]')
    if close_buttons:
        print(f"Found {len(close_buttons)} potential overlay(s). Attempting to close...")
        for button in close_buttons:
            try:
                button.click()
                print("Clicked a close button.")
                time.sleep(1)
            except Exception as e:
                print(f"Could not click close button: {e}")
                try:
                    driver.execute_script("arguments[0].click();", button)
                    print("Used JavaScript to click close button.")
                    time.sleep(1)
                except Exception as js_e:
                    print(f"JavaScript click also failed: {js_e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    # Find all product name anchors
    product_anchors = soup.find_all('a', class_=lambda x: x and 'product-tile__name' in x)
    if not product_anchors:
        print("No product anchors found with specific selector.")
    for anchor in product_anchors:
        ellip_span = anchor.find('span', class_='ellip')
        if ellip_span:
            # Get all text nodes (including those in nested spans), join with space
            words = [w.get_text(strip=True) for w in ellip_span.find_all('span')]
            title_text = ' '.join(words)
            results.append(title_text)
    return results

def main():
    selenium_crawl(URL, max_pages=20)

if __name__ == "__main__":
    main() 