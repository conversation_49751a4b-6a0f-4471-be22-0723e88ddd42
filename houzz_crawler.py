#!/usr/bin/env python3
import time
import random
import argparse
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Default URLs to crawl
DEFAULT_URLS = [
    "https://www.houzz.com/products/armoires-and-wardrobes"
]

class HouzzCrawler:
    def __init__(self, urls, output_file=None, output_dir='houzz', delay_range=(0.5, 1)):
        self.urls = urls if isinstance(urls, list) else [urls]
        self.delay_range = delay_range
        self.output_dir = output_dir
        self.output_files = []  # List to store all output files
        
        # Setup Selenium
        self.setup_selenium()
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create a separate output file for each URL
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for i, url in enumerate(self.urls):
            # Extract a name from the URL for the filename
            url_parts = url.split('/')
            product_type = url_parts[-1] if url_parts[-1] else f"product_{i+1}"
            
            # Create output filename
            if output_file is None:
                file_name = f"{output_dir}/{product_type}_{timestamp}.txt"
            else:
                base, ext = os.path.splitext(output_file)
                file_name = f"{output_dir}/{base}_{product_type}{ext}"
                
            self.output_files.append(file_name)
            
            # Create or clear the output file
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write("# Houzz Product Titles\n")
                f.write(f"# Source: {url}\n")
                f.write(f"# Scraped: {datetime.now().strftime('%Y-%m-%d')}\n\n")
                
            print(f"Created file for storing product titles from {product_type}: {os.path.abspath(file_name)}")

    def setup_selenium(self):
        """Set up the Selenium WebDriver with optimized settings"""
        # Configure Chrome options
        options = Options()
        
        # Performance optimizations
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-infobars")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-popup-blocking")
        # make it headless
        options.add_argument("--headless")
        
        # Anti-detection measures
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--start-maximized")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        
        # Set a realistic user agent
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        
        # Create the WebDriver
        self.driver = webdriver.Chrome(options=options)
        
        # Add undetected settings
        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
            """
        })
        
        # Set shorter implicit wait time
        self.driver.implicitly_wait(5)

    def get_page(self, url):
        """Navigate to the URL and get page content"""
        try:
            print(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Reduce delay
            time.sleep(0.5)
            
            # Use a faster scroll method
            self.fast_scroll()
            
            return True
        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return False
    
    def fast_scroll(self, max_scroll_attempts=3):
        """Optimized scrolling to quickly load dynamic content"""
        try:
            # Get initial page height
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            # Scroll in fewer steps to speed up the process
            for _ in range(max_scroll_attempts):
                # Scroll to bottom quickly
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # Short wait for content to load
                time.sleep(0.3)
                
                # Calculate new scroll height and compare with last scroll height
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                
                # Break if no more content loaded
                if new_height == last_height:
                    break
                    
                last_height = new_height
                
        except Exception as e:
            print(f"Error during scrolling: {e}")

    def get_product_titles(self):
        """Extract product titles using direct selectors for better performance"""
        try:
            # Use a more direct and efficient approach to fetch all titles at once
            products = []
            
            # Primary selector for product titles - directly target the most likely elements
            selectors = [
                "a.hz-product-card__product-title",
                ".hz-product-card__product-title",
                ".hz-product-card a[title]"
            ]
            
            # Try each selector once, don't retry or nest multiple times
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"Found {len(elements)} products using selector: {selector}")
                        for elem in elements:
                            # Get either text or title attribute efficiently
                            title = elem.text.strip() or elem.get_attribute('title')
                                
                            # Simple minimal validation - just ensure it's not empty
                            if title and len(title) > 3 and title not in products:
                                products.append(title)
                        
                        # If we found products with this selector, don't try others
                        if products:
                            break
                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue
            
            # If primary selectors fail, try a fallback approach
            if not products:
                # Fallback: Look directly for product cards and extract their titles
                product_cards = self.driver.find_elements(By.CSS_SELECTOR, ".hz-product-card, .hz-br-product-card")
                for card in product_cards[:50]:  # Limit to first 50 for speed
                    try:
                        # Try to find a title element within this card
                        title_elem = card.find_element(By.CSS_SELECTOR, "a[title], .hz-product-card__product-title, h3, h4")
                        title = title_elem.text.strip() or title_elem.get_attribute('title')
                        if title and len(title) > 3 and title not in products:
                            products.append(title)
                    except:
                        continue
            
            print(f"Found {len(products)} product titles")
            return products
        
        except Exception as e:
            print(f"Error extracting product titles: {e}")
            return []

    def get_next_page_url(self):
        """Navigate to the next page using simplified pagination detection"""
        try:
            # Look for the next page button directly to avoid multiple selector attempts
            next_selectors = [
                "a.hz-pagination-link--next",
                "a[aria-label='Next Page']",
                "a.next-page",
                "a[rel='next']"
            ]
            
            for selector in next_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href:
                            print(f"Found next page link: {href}")
                            return href
                except:
                    continue
            
            # Simple check for a link with "Next" text
            try:
                next_link = self.driver.find_element(By.XPATH, "//a[contains(text(), 'Next')]")
                href = next_link.get_attribute('href')
                if href:
                    return href
            except:
                pass
                
            print("No next page link found, this might be the last page")
            return None
            
        except Exception as e:
            print(f"Error finding next page URL: {e}")
            return None

    def save_titles(self, titles, output_file):
        """Append titles to the output file and show sample in console"""
        if not titles:
            return
            
        with open(output_file, 'a', encoding='utf-8') as f:
            for title in titles:
                f.write(f"{title}\n")
                
        # Print sample of titles for verification
        print("\nSample titles from this page:")
        for i, title in enumerate(titles[:3], 1):
            print(f"  {i}. {title}")
        if len(titles) > 3:
            print(f"  ... and {len(titles) - 3} more")

    def sort_output_file(self, output_file):
        """Sort the product titles in the output file alphabetically"""
        try:
            # Skip the header (first 4 lines)
            with open(output_file, 'r', encoding='utf-8') as f:
                header_lines = [f.readline() for _ in range(4)]
                product_lines = [line.strip() for line in f if line.strip()]
            
            # Sort the product lines
            product_lines.sort()
            
            # Create a new sorted filename
            base_name = os.path.splitext(output_file)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            sorted_file = f"{base_name}_sorted_{timestamp}.txt"
            
            # Write the sorted file
            with open(sorted_file, 'w', encoding='utf-8') as f:
                f.writelines(line + '\n' for line in header_lines)
                f.writelines(line + '\n' for line in product_lines)
                
            print(f"\nCreated sorted output file: {os.path.abspath(sorted_file)}")
            
            # Delete the original file
            os.remove(output_file)
            print(f"Original file deleted: {os.path.abspath(output_file)}")
            
            return sorted_file
        except Exception as e:
            print(f"Error sorting output file: {e}")
            return None

    def crawl_all_urls(self, max_pages=None):
        """Crawl all URLs in the list"""
        total_pages = 0
        total_products = 0
        sorted_files = []
        
        try:
            for i, url in enumerate(self.urls, 1):
                print(f"\n\n{'=' * 50}")
                print(f"Processing URL {i}/{len(self.urls)}: {url}")
                print(f"{'=' * 50}\n")
                
                # Get the corresponding output file for this URL
                output_file = self.output_files[i-1]
                
                # Crawl this URL
                pages, products = self.crawl_single_url(url, output_file, max_pages)
                
                total_pages += pages
                total_products += products
                
                # Create a sorted version of the file if products were found
                # if products > 0:
                #     sorted_file = self.sort_output_file(output_file)
                #     if sorted_file:
                #         sorted_files.append(sorted_file)
                #         print(f"A sorted version of the results has been saved to: {sorted_file}")
                
                # Add some delay between processing different URLs
                if i < len(self.urls):
                    delay = random.uniform(1.0, 2.0)  # Slightly longer delay between URLs
                    print(f"\nWaiting {delay:.1f} seconds before processing next URL...")
                    time.sleep(delay)
        
        finally:
            # Clean up resources
            print("\nClosing browser and cleaning up resources...")
            try:
                self.driver.quit()
            except Exception as e:
                print(f"Error closing browser: {e}")
            
            print(f"\n==== Crawl completed for all URLs ====")
            print(f"Processed {len(self.urls)} URLs, {total_pages} pages with {total_products} total products.")
            print(f"Results saved to separate files for each URL:")
            for file in sorted_files:
                print(f"- {os.path.abspath(file)}")
    
    def crawl_single_url(self, base_url, output_file, max_pages=None):
        """Crawl a single URL and its pagination"""
        try:
            current_url = base_url
            page_count = 0
            product_count = 0
            consecutive_empty_pages = 0
            max_empty_pages = 2  # Stop after this many consecutive pages with no products
            
            print(f"Starting crawl at {current_url}")
            print(f"Saving results to: {output_file}")
            
            while current_url:
                page_count += 1
                print(f"\n==== Crawling page {page_count}: {current_url} ====")
                
                # Check if we've reached the max pages limit
                if max_pages and page_count > max_pages:
                    print(f"Reached maximum page limit ({max_pages})")
                    break
                    
                # Navigate to the page
                success = self.get_page(current_url)
                if not success:
                    print("Failed to load page. Stopping crawl.")
                    break
                
                # Extract product titles
                titles = self.get_product_titles()
                title_count = len(titles)
                product_count += title_count
                
                print(f"Found {title_count} products on this page. Total so far: {product_count}")
                
                # Check for consecutive empty pages
                if title_count == 0:
                    consecutive_empty_pages += 1
                    if consecutive_empty_pages >= max_empty_pages:
                        print(f"Reached {max_empty_pages} consecutive empty pages. Stopping crawl.")
                        break
                else:
                    consecutive_empty_pages = 0  # Reset counter when we find products
                
                # Save the titles to the specific output file for this URL
                self.save_titles(titles, output_file)
                
                # Get the next page URL
                next_url = self.get_next_page_url()
                
                if not next_url or next_url == current_url:
                    print("No next page found or same URL. End of crawl.")
                    break
                    
                # Update the current URL
                current_url = next_url
                
                # Add a minimal delay between requests
                delay = random.uniform(self.delay_range[0], self.delay_range[1])
                print(f"Waiting {delay:.1f} seconds before next request...")
                time.sleep(delay)
            
            return page_count, product_count
        
        except Exception as e:
            print(f"Error crawling {base_url}: {e}")
            return 0, 0

def main():
    parser = argparse.ArgumentParser(description='Fast Houzz product listings crawler')
    parser.add_argument('--urls', type=str, nargs='+', default=DEFAULT_URLS,
                        help='List of URLs to crawl')
    parser.add_argument('--url-file', type=str, default=None,
                        help='Path to a text file with one URL per line')
    parser.add_argument('--output', type=str, default=None,
                        help='Base output file name (will be modified for each URL)')
    parser.add_argument('--max-pages', type=int, default=None,
                        help='Maximum number of pages to crawl per URL')
    parser.add_argument('--output-dir', type=str, default='houzz',
                        help='Directory to store output files')
    parser.add_argument('--min-delay', type=float, default=0.5,
                        help='Minimum delay between requests in seconds')
    parser.add_argument('--max-delay', type=float, default=1.0,
                        help='Maximum delay between requests in seconds')
    
    args = parser.parse_args()
    
    # Collect URLs from both arguments and file (if provided)
    urls = args.urls
    
    # If a URL file is provided, read URLs from it
    if args.url_file:
        try:
            with open(args.url_file, 'r') as f:
                file_urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                urls.extend(file_urls)
                print(f"Loaded {len(file_urls)} URLs from {args.url_file}")
        except Exception as e:
            print(f"Error reading URL file: {e}")
    
    # Remove any duplicate URLs
    urls = list(dict.fromkeys(urls))
    
    # Print some startup info
    print("Fast Houzz Product Crawler")
    print(f"Number of URLs to process: {len(urls)}")
    print("URLs to crawl:")
    for i, url in enumerate(urls, 1):
        print(f"  {i}. {url}")
    
    if args.max_pages:
        print(f"Will crawl up to {args.max_pages} pages per URL")
    print(f"Output directory: {os.path.abspath(args.output_dir)}")
    print(f"Delay between requests: {args.min_delay}-{args.max_delay} seconds")
    print(f"Each URL will be saved to a separate output file")
    print()
    
    # Start the crawler
    crawler = HouzzCrawler(
        urls, 
        args.output, 
        args.output_dir,
        delay_range=(args.min_delay, args.max_delay)
    )
    crawler.crawl_all_urls(args.max_pages)

if __name__ == "__main__":
    main()