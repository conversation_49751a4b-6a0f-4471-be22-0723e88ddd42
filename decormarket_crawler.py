#!/usr/bin/env python3
import time
import os
import datetime
import random
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here
URL = "https://www.decormarket.com/collections/outdoor-rugs"

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        # --- Anti-bot evasion settings ---
        user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/123.0.0.0 Safari/537.36"
        )
        options.add_argument(f'user-agent={user_agent}')
        options.add_argument(f'--user-data-dir=/tmp/chrome_profile_{random.randint(10000,99999)}')
        options.add_argument('window-size=1200,900')
        options.add_argument('--lang=en-US,en')
        options.add_argument('--disable-blink-features=AutomationControlled')
        # Do NOT use headless mode
        # --- End anti-bot evasion settings ---
        chrome_version = 135  # Update this to match your Chrome version
        driver = uc.Chrome(version_main=chrome_version, options=options)
        
        driver.get(url)
        time.sleep(random.uniform(2, 4))
        
        # Accept cookies if the dialog appears
        try:
            cookie_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Accept")]'))
            )
            cookie_button.click()
            print("Accepted cookies")
            time.sleep(random.uniform(1, 2))
        except TimeoutException:
            print("No cookie dialog found or it was already accepted")
        
        # Allow some time for the page to fully load
        print("Waiting for page to load completely...")
        time.sleep(5)
        
        # Try to close any overlays/popups that might appear
        close_overlays(driver)
        time.sleep(random.uniform(1, 2))
        
        # Create output directory if it doesn't exist
        output_dir = 'decormarket'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"decormarket_{timestamp}.txt")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Decor Market - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing product titles: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        
        while current_page <= max_pages and not last_page_reached:
            print(f"\n==== Scraping page {current_page} ====")
            
            # Scroll down slowly to load all products
            for i in range(10):
                driver.execute_script(f"window.scrollTo(0, {i * 500});")
                time.sleep(random.uniform(0.2, 0.5))
            
            page_results = process_page(driver.page_source)
            time.sleep(random.uniform(1, 2))
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for product in page_results:
                        product_count += 1
                        title = product['title']
                        # Write only the product title without the price
                        f.write(f"{title}\n")
                
                for i, product in enumerate(page_results[:5], 1):
                    title = product['title']
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Use Selenium to click the next pagination link if present
            try:
                next_button = None
                try:
                    # Updated pagination selector based on the actual HTML structure
                    next_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'a.pagination-custom__next'))
                    )
                except TimeoutException:
                    print("No next page button found. Reached the last page.")
                    last_page_reached = True
                    break
                
                if next_button:
                    print("Clicking next pagination button...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                    time.sleep(random.uniform(1, 2))
                    try:
                        next_button.click()
                    except ElementClickInterceptedException:
                        print("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_button)
                    
                    # Wait longer after pagination to ensure the new page loads
                    print("Waiting for next page to load...")
                    time.sleep(random.uniform(3, 5))
                    
                    # Scroll back to top of page
                    driver.execute_script("window.scrollTo(0, 0);")
                    
                    # Additional wait for elements to load after scrolling back up
                    time.sleep(1)
                    
                    current_page += 1
                else:
                    print("No next pagination button found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                print(f"Error during pagination: {e}")
                last_page_reached = True
                break
                
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All product titles have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nSample Product Titles:")
            for i, product in enumerate(all_results[:10], 1):
                print(f"{i}. {product['title']}")
            if len(all_results) > 10:
                print(f"... and {len(all_results) - 10} more")
            print(f"\nTotal products found: {len(all_results)}")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver:
            print("Closing browser...")
            driver.quit()

def close_overlays(driver):
    # Try to close various types of overlays common on e-commerce sites
    selectors = [
        '//button[@aria-label="Close"]',
        '//div[@aria-label="Close"]',
        '//button[contains(text(), "Close")]',
        '//button[contains(@class, "close")]',
        '//span[contains(@class, "close")]',
        '//div[contains(@class, "popup-close")]'
    ]
    
    for selector in selectors:
        try:
            close_buttons = driver.find_elements(By.XPATH, selector)
            if close_buttons:
                print(f"Found {len(close_buttons)} potential overlay(s) with selector {selector}. Attempting to close...")
                for button in close_buttons:
                    try:
                        button.click()
                        print("Clicked a close button.")
                        time.sleep(random.uniform(0.5, 1))
                    except Exception as e:
                        print(f"Could not click close button: {e}")
                        try:
                            driver.execute_script("arguments[0].click();", button)
                            print("Used JavaScript to click close button.")
                            time.sleep(random.uniform(0.5, 1))
                        except Exception as js_e:
                            print(f"JavaScript click also failed: {js_e}")
        except Exception as e:
            print(f"Error finding overlay close buttons with selector {selector}: {e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    
    # Look for products with the exact selector provided by user
    product_titles = soup.find_all('div', class_='product-item__title')
    
    if product_titles:
        print(f"Found {len(product_titles)} products using div.product-item__title selector")
        for title_elem in product_titles:
            try:
                # Get the product title text
                full_title = title_elem.get_text(strip=True)
                
                # Process the title to only include text before the comma
                if ',' in full_title:
                    title = full_title.split(',')[0].strip()
                else:
                    title = full_title
                
                # Try to find the price - start by finding the parent container
                parent = title_elem.parent
                while parent and parent.name != 'body':
                    # Look for price within this parent
                    price_elem = parent.find('span', class_='price') or parent.find('div', class_='price')
                    if not price_elem:
                        price_elem = parent.find('span', class_='price-item--regular')
                    
                    if price_elem:
                        price_text = price_elem.get_text(strip=True)
                        # Extract just the numeric part of the price
                        price = ''.join(c for c in price_text if c.isdigit() or c == '.')
                        if price.count('.') > 1:
                            # Handle cases where there might be multiple periods
                            parts = price.split('.')
                            price = parts[0] + '.' + parts[1]
                        
                        if title:
                            results.append({
                                'title': title,
                                'price': price
                            })
                        break
                    
                    # If no price found, move up to the next parent
                    parent = parent.parent
                
                # If we couldn't find a price, add the product with unknown price
                if not results or results[-1]['title'] != title:
                    results.append({
                        'title': title,
                        'price': 'unknown'
                    })
            except Exception as e:
                print(f"Error extracting product info: {e}")
        
        return results
    
    # If the primary selector didn't work, try the previous selectors as fallback
    print("No products found with div.product-item__title selector. Trying alternative selectors...")
    
    # Based on decormarket.com structure - product cards
    product_cards = soup.find_all('div', class_='card')
    
    if not product_cards:
        print("No product cards found. Trying more alternative selectors...")
        # Try more alternative selectors
        product_cards = soup.find_all('div', class_='card-wrapper')
        if not product_cards:
            product_cards = soup.find_all('div', class_='product-item')
    
    for card in product_cards:
        try:
            # Find product title with various potential selectors
            title_elem = (
                card.find('div', class_='product-item__title') or
                card.find('h3', class_='card__heading') or 
                card.find('div', class_='product-name') or
                card.find('a', class_='full-unstyled-link')
            )
            
            # Find product price with various potential selectors
            price_elem = (
                card.find('span', class_='price-item--regular') or 
                card.find('div', class_='price') or
                card.find('span', class_='price')
            )
            
            if title_elem:
                full_title = title_elem.get_text(strip=True)
                
                # Process the title to only include text before the comma
                if ',' in full_title:
                    title = full_title.split(',')[0].strip()
                else:
                    title = full_title
                
                price = 'unknown'
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    # Extract just the numeric part of the price
                    price = ''.join(c for c in price_text if c.isdigit() or c == '.')
                    if price.count('.') > 1:
                        # Handle cases where there might be multiple periods
                        parts = price.split('.')
                        price = parts[0] + '.' + parts[1]
                
                if title:
                    results.append({
                        'title': title,
                        'price': price
                    })
        except Exception as e:
            print(f"Error extracting product info: {e}")
    
    if not results:
        print("No products found with any of the selectors. The site structure might have changed.")
        print("Dumping some HTML to help debug:")
        # Print a small section of the HTML to help debug
        print(soup.prettify()[:1000])
    
    return results

def main():
    print("Decor Market Product Crawler")
    custom_url = URL
    max_pages = 10
    
    print(f"\nStarting crawler for: {custom_url}")
    print(f"Will crawl up to {max_pages} pages\n")
    
    # Ensure decormarket directory exists
    os.makedirs('decormarket', exist_ok=True)
    print(f"Decormarket directory created/verified at: {os.path.abspath('decormarket')}")
    
    selenium_crawl(custom_url, max_pages=max_pages)

if __name__ == "__main__":
    main() 