#!/usr/bin/env python3
"""
Houzz Photos Auto-Pagination Engine - Automatically crawl until last page
Continues crawling until no more pages are found
"""

import sys
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs, urle<PERSON><PERSON>, urlunparse
import json
from datetime import datetime
import time
import random
import re

class HouzzAutoPaginator:
    def __init__(self, base_url, delay_range=(1, 3), max_empty_pages=3, timeout=30):
        self.base_url = base_url
        self.delay_range = delay_range
        self.max_empty_pages = max_empty_pages  # Stop after X consecutive empty pages
        self.timeout = timeout
        self.session = requests.Session()
        
        # Initialize output files
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        parsed_url = urlparse(base_url)
        url_path = parsed_url.path.strip('/').replace('/', '_')
        if not url_path:
            url_path = "houzz_photos"
        
        self.json_file = f"{url_path}_auto_paginated_{self.timestamp}.json"
        self.txt_file = f"{url_path}_auto_paginated_{self.timestamp}.txt"
        
        # Initialize files
        self.init_output_files()
        
        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })

    def build_page_url(self, page_num):
        """Build URL with pagination parameter"""
        parsed = urlparse(self.base_url)
        query_params = parse_qs(parsed.query)
        query_params['pg'] = [str(page_num)]
        new_query = urlencode(query_params, doseq=True)
        new_parsed = parsed._replace(query=new_query)
        return urlunparse(new_parsed)

    def extract_photos_from_page(self, url, page_num):
        """Extract photo links from a single page with enhanced detection"""
        try:
            print(f"📄 Page {page_num}: Fetching {url}")
            
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 404:
                print(f"❌ Page {page_num}: 404 Not Found - Likely reached last page")
                return [], False, True  # photos, has_content, is_404
            
            if response.status_code != 200:
                print(f"❌ Page {page_num}: HTTP {response.status_code}")
                return [], False, False
            
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for "no results" or "end of results" indicators
            if self.is_end_page(soup, page_num):
                print(f"🛑 Page {page_num}: End of results detected")
                return [], False, False
            
            # Extract photo links
            photo_links = self.extract_photo_links(soup, page_num)
            
            # Enhanced next page detection
            has_next = self.detect_next_page(soup, page_num, len(photo_links))
            
            print(f"✅ Page {page_num}: Found {len(photo_links)} photo links")
            
            return photo_links, has_next, False
            
        except requests.RequestException as e:
            print(f"❌ Page {page_num}: Request error - {e}")
            return [], False, False
        except Exception as e:
            print(f"❌ Page {page_num}: Unexpected error - {e}")
            return [], False, False

    def extract_photo_links(self, soup, page_num):
        """Extract photo links from soup with enhanced title extraction"""
        photo_links = []
        pattern = "https://www.houzz.com/photos/"
        
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link['href']
            
            # Convert relative URLs to absolute
            if href.startswith('/photos/'):
                href = urljoin('https://www.houzz.com', href)
            
            # Only include if it matches the pattern
            if href.startswith(pattern):
                text = link.get_text(strip=True)
                title = link.get('title', '')
                
                # Extract meaningful title from URL
                url_title = self.extract_title_from_url(href)
                
                # Use the best available title
                best_title = ""
                if url_title:
                    best_title = url_title
                elif title:
                    best_title = title
                elif text:
                    best_title = text
                
                photo_links.append({
                    'href': href,
                    'text': text,
                    'title': title,
                    'url_title': url_title,
                    'best_title': best_title,
                    'page': page_num
                })
        
        return photo_links

    def is_end_page(self, soup, page_num):
        """Check if this is the end page with no more results"""
        # Look for common "no results" indicators
        no_results_indicators = [
            'No results found',
            'No more results',
            'End of results',
            'Nothing found',
            'No photos found',
            'no-results',
            'empty-results',
            'end-of-content'
        ]
        
        page_text = soup.get_text().lower()
        
        for indicator in no_results_indicators:
            if indicator.lower() in page_text:
                return True
        
        # Check for empty content areas
        content_selectors = [
            '.photo-grid',
            '.results-container',
            '.photo-results',
            '.content-grid',
            '[data-testid="photo-grid"]'
        ]
        
        for selector in content_selectors:
            content_area = soup.select_one(selector)
            if content_area and not content_area.find_all('a'):
                return True
        
        return False

    def detect_next_page(self, soup, current_page, photos_found):
        """Enhanced detection of whether there's a next page"""
        # Method 1: Look for explicit next page links
        next_page_selectors = [
            f'a[href*="pg={current_page + 1}"]',
            'a[aria-label*="Next"]',
            'a[title*="Next"]',
            '.pagination .next',
            '.pager .next',
            'a.next-page'
        ]
        
        for selector in next_page_selectors:
            if soup.select(selector):
                return True
        
        # Method 2: Look for pagination with higher page numbers
        try:
            page_links = soup.find_all('a', href=True)
            max_page_found = current_page
            
            for link in page_links:
                href = link.get('href', '')
                if 'pg=' in href:
                    try:
                        page_match = re.search(r'pg=(\d+)', href)
                        if page_match:
                            page_num = int(page_match.group(1))
                            max_page_found = max(max_page_found, page_num)
                    except:
                        continue
            
            if max_page_found > current_page:
                return True
        except:
            pass
        
        # Method 3: If we found photos, likely there are more pages
        # (unless it's significantly fewer than expected)
        if photos_found > 10:  # Reasonable threshold
            return True
        
        # Method 4: Look for "Load more" or infinite scroll indicators
        load_more_selectors = [
            '.load-more',
            '.show-more',
            '[data-infinite-scroll]',
            '.infinite-scroll'
        ]
        
        for selector in load_more_selectors:
            if soup.select(selector):
                return True
        
        return False

    def crawl_all_pages(self):
        """Main crawling function - continues until last page with incremental saving"""
        all_photo_links = []
        successful_pages = []
        empty_page_count = 0
        page_num = 1
        seen_urls = set()  # Track unique URLs
        
        print(f"🚀 AUTO-PAGINATION ENGINE STARTED (INCREMENTAL SAVE MODE)")
        print(f"Base URL: {self.base_url}")
        print(f"Will continue until last page is reached")
        print(f"Max consecutive empty pages: {self.max_empty_pages}")
        print(f"Delay range: {self.delay_range[0]}-{self.delay_range[1]} seconds")
        print("-" * 70)
        
        start_time = datetime.now()
        
        while True:
            page_url = self.build_page_url(page_num)
            
            # Extract photos from current page
            page_photos, has_next, is_404 = self.extract_photos_from_page(page_url, page_num)
            
            # Handle 404 (likely reached end)
            if is_404:
                print(f"🛑 Stopping: Page {page_num} returned 404")
                break
            
            # Handle photos found
            if page_photos:
                # Filter out duplicates for this session
                unique_page_photos = []
                for photo in page_photos:
                    if photo['href'] not in seen_urls:
                        seen_urls.add(photo['href'])
                        unique_page_photos.append(photo)
                        all_photo_links.append(photo)
                
                if unique_page_photos:
                    successful_pages.append(page_num)
                    empty_page_count = 0  # Reset empty page counter
                    
                    # Append results immediately after each page
                    self.append_page_results(unique_page_photos, page_num, len(all_photo_links))
                    
                    print(f"📊 Page {page_num}: {len(unique_page_photos)} new unique links")
                    print(f"📊 Total unique links so far: {len(all_photo_links)}")
                else:
                    print(f"⚠️  Page {page_num}: All links were duplicates")
                    empty_page_count += 1
            else:
                empty_page_count += 1
                print(f"⚠️  Page {page_num}: No photos found (empty count: {empty_page_count})")
            
            # Check if we should stop
            if empty_page_count >= self.max_empty_pages:
                print(f"🛑 Stopping: {self.max_empty_pages} consecutive empty pages")
                break
            
            if not has_next and page_num > 1:
                print(f"🛑 Stopping: No next page indicator found")
                break
            
            # Prepare for next page
            page_num += 1
            
            # Add delay between requests
            delay = random.uniform(*self.delay_range)
            print(f"⏱️  Waiting {delay:.1f} seconds before page {page_num}...")
            time.sleep(delay)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Finalize files with summary
        self.finalize_files(successful_pages, duration, len(all_photo_links))
        
        print(f"\n{'='*70}")
        print(f"🏁 AUTO-PAGINATION COMPLETE")
        print(f"{'='*70}")
        print(f"Duration: {duration}")
        print(f"Pages attempted: {page_num - 1}")
        print(f"Successful pages: {successful_pages}")
        print(f"Total unique photo links: {len(all_photo_links)}")
        print(f"Results saved incrementally to:")
        print(f"  📁 {self.json_file}")
        print(f"  📄 {self.txt_file}")
        
        return all_photo_links, successful_pages, duration

    def init_output_files(self):
        """Initialize output files with headers"""
        # Initialize JSON file with metadata
        initial_data = {
            'base_url': self.base_url,
            'started_at': datetime.now().isoformat(),
            'crawl_method': 'auto_pagination_with_incremental_save',
            'pages': [],
            'total_photos': 0,
            'photo_links': []
        }
        
        with open(self.json_file, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)
        
        # Initialize text file with headers
        with open(self.txt_file, 'w', encoding='utf-8') as f:
            f.write(f"# Houzz Photos Auto-Pagination Results (Incremental)\n")
            f.write(f"# Base URL: {self.base_url}\n")
            f.write(f"# Pattern: https://www.houzz.com/photos/\n")
            f.write(f"# Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Mode: Incremental saving after each page\n\n")
        
        print(f"📁 Output files initialized:")
        print(f"   - JSON: {self.json_file}")
        print(f"   - TXT:  {self.txt_file}")

    def append_page_results(self, page_photos, page_num, all_photos_count):
        """Append results from current page to files"""
        if not page_photos:
            return
        
        # Update JSON file
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Add current page info
            data['pages'].append(page_num)
            data['total_photos'] = all_photos_count
            data['last_updated'] = datetime.now().isoformat()
            
            # Add new photo links (avoiding duplicates)
            existing_hrefs = set(link['href'] for link in data['photo_links'])
            new_links = [link for link in page_photos if link['href'] not in existing_hrefs]
            data['photo_links'].extend(new_links)
            
            # Save updated JSON
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"❌ Error updating JSON file: {e}")
        
        # Append to text file
        try:
            with open(self.txt_file, 'a', encoding='utf-8') as f:
                f.write(f"## PAGE {page_num} ({len(page_photos)} links) - {datetime.now().strftime('%H:%M:%S')}\n")
                f.write("-" * 60 + "\n")
                
                for link in page_photos:
                    if link.get('best_title'):
                        f.write(f"{link['best_title']}\n")
                
                f.write(f"\n# Total unique links so far: {all_photos_count}\n\n")
            
        except Exception as e:
            print(f"❌ Error updating text file: {e}")
        
        print(f"💾 Page {page_num} results appended to files")

    def finalize_files(self, successful_pages, duration, total_unique_photos):
        """Finalize files with summary information"""
        # Update JSON with final stats
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            data['completed_at'] = datetime.now().isoformat()
            data['duration_seconds'] = duration.total_seconds()
            data['pages_crawled'] = successful_pages
            data['total_pages'] = len(successful_pages)
            data['final_unique_photos'] = total_unique_photos
            
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Error finalizing JSON: {e}")
        
        # Add summary to text file
        try:
            with open(self.txt_file, 'a', encoding='utf-8') as f:
                f.write("\n" + "="*70 + "\n")
                f.write("CRAWL SUMMARY\n")
                f.write("="*70 + "\n")
                f.write(f"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Duration: {duration}\n")
                f.write(f"Pages crawled: {successful_pages}\n")
                f.write(f"Final unique photos: {total_unique_photos}\n")
                f.write(f"Average per page: {total_unique_photos/len(successful_pages):.1f}\n")
        except Exception as e:
            print(f"❌ Error finalizing text file: {e}")

    def remove_duplicates(self, photo_links):
        """Remove duplicate photo links (now handled incrementally)"""
        # This is now handled incrementally during crawling
        # But we keep this method for compatibility
        seen_urls = set()
        unique_links = []
        
        for link in photo_links:
            if link['href'] not in seen_urls:
                seen_urls.add(link['href'])
                unique_links.append(link)
        
        duplicates_removed = len(photo_links) - len(unique_links)
        if duplicates_removed > 0:
            print(f"🔄 Final deduplication: {duplicates_removed} duplicates found")
        
        return unique_links

    def extract_title_from_url(self, href):
        """Extract meaningful title from Houzz photo URL"""
        try:
            # Parse URL to get the path
            parsed = urlparse(href)
            path = parsed.path
            
            # Extract the part after /photos/
            if '/photos/' in path:
                # Get everything after /photos/
                after_photos = path.split('/photos/', 1)[1]
                
                # Remove technical suffixes like -phbr0-bp~t_24680
                # Split by - and filter out technical parts
                parts = after_photos.split('-')
                meaningful_parts = []
                
                for part in parts:
                    # Skip parts that look like technical IDs
                    if (part.startswith('phbr') or 
                        part.startswith('bp~') or 
                        part.startswith('t_') or 
                        part.startswith('a_') or
                        part.startswith('bl~') or
                        part.startswith('lbl') or
                        part.startswith('pj-vj~') or
                        part.startswith('phvw-vp~') or
                        part.startswith('Phvw') or
                        part.isdigit() or
                        '~' in part):
                        break  # Stop when we hit technical parts
                    
                    # Add meaningful parts
                    if part and not part.isdigit():
                        meaningful_parts.append(part)
                
                if meaningful_parts:
                    # Join parts and clean up
                    title = ' '.join(meaningful_parts)
                    # Replace remaining dashes and underscores with spaces
                    title = title.replace('-', ' ').replace('_', ' ')
                    # Clean up multiple spaces
                    title = ' '.join(title.split())
                    # Capitalize words
                    title = title.title()
                    # Fix common words that shouldn't be capitalized
                    title = title.replace(' And ', ' and ')
                    title = title.replace(' Or ', ' or ')
                    title = title.replace(' The ', ' the ')
                    title = title.replace(' Of ', ' of ')
                    title = title.replace(' In ', ' in ')
                    title = title.replace(' On ', ' on ')
                    title = title.replace(' At ', ' at ')
                    
                    return title
            
            return ""
            
        except Exception as e:
            return ""

    # Removed save_results method - now using incremental saving

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Auto-crawl Houzz photos until last page')
    parser.add_argument('url', nargs='?', 
                       default="https://www.houzz.com/photos/bedroom-ideas-and-designs-phbr0-bp~t_715",
                       help='Base URL to crawl')
    parser.add_argument('--min-delay', type=float, default=1.0, 
                       help='Minimum delay between pages (default: 1.0)')
    parser.add_argument('--max-delay', type=float, default=3.0, 
                       help='Maximum delay between pages (default: 3.0)')
    parser.add_argument('--max-empty', type=int, default=3,
                       help='Stop after X consecutive empty pages (default: 3)')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Request timeout in seconds (default: 30)')
    
    args = parser.parse_args()
    
    print("🤖 Houzz Auto-Pagination Engine")
    print(f"Target Pattern: https://www.houzz.com/photos/")
    print(f"Base URL: {args.url}")
    
    # Create paginator
    paginator = HouzzAutoPaginator(
        base_url=args.url,
        delay_range=(args.min_delay, args.max_delay),
        max_empty_pages=args.max_empty,
        timeout=args.timeout
    )
    
    try:
        # Crawl all pages (results are saved incrementally)
        photo_links, successful_pages, duration = paginator.crawl_all_pages()
        
        if photo_links:
            # Final deduplication check (should be minimal since we handle it incrementally)
            unique_photos = paginator.remove_duplicates(photo_links)
            
            # Show final summary
            print(f"\n📋 FINAL SUMMARY:")
            print(f"   Duration: {duration}")
            print(f"   Pages crawled: {len(successful_pages)}")
            print(f"   Total unique photo links: {len(unique_photos)}")
            print(f"   Average links per page: {len(unique_photos)/len(successful_pages):.1f}")
            print(f"   Files updated incrementally during crawl")
            
            # Show sample links
            print(f"\n🔗 Sample photo links:")
            for i, link in enumerate(unique_photos[:3], 1):
                print(f"   {i}. {link['href']}")
                if link['text']:
                    print(f"      Text: {link['text'][:60]}...")
        else:
            print("❌ No photo links found")
            
    except KeyboardInterrupt:
        print(f"\n\n⚠️  Crawling interrupted by user")
        print(f"📁 Partial results saved to:")
        print(f"   - {paginator.json_file}")
        print(f"   - {paginator.txt_file}")
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        print(f"📁 Partial results may be saved to:")  
        print(f"   - {paginator.json_file}")
        print(f"   - {paginator.txt_file}")

if __name__ == "__main__":
    main()
