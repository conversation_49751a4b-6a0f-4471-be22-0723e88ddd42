#!/usr/bin/env python3
import os
import sys
import argparse
from datetime import datetime

def sort_keywords_by_length(input_file_path, output_file_path=None):
    """
    Read keywords from a text file, sort them from longest to shortest,
    and save the sorted keywords to a new file.
    
    Args:
        input_file_path: Path to the input text file containing keywords
        output_file_path: Path to save the sorted keywords (optional)
    
    Returns:
        Path to the output file
    """
    if not os.path.exists(input_file_path):
        print(f"Error: Input file '{input_file_path}' does not exist.")
        return None
    
    try:
        # Read keywords from the input file
        with open(input_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Skip header lines that start with '#'
        keywords = []
        header_lines = []
        for line in lines:
            stripped_line = line.strip()
            if stripped_line.startswith('#'):
                header_lines.append(line)
            elif stripped_line:  # Skip empty lines
                keywords.append(stripped_line)
        
        # Sort keywords by length (longest to shortest)
        sorted_keywords = sorted(keywords, key=len, reverse=True)
        
        # Generate output file path if not provided
        if not output_file_path:
            directory = os.path.dirname(input_file_path)
            filename = os.path.basename(input_file_path)
            name, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file_path = os.path.join(directory, f"{name}_sorted_{timestamp}{ext}")
        
        # Write sorted keywords to the output file
        with open(output_file_path, 'w', encoding='utf-8') as f:
            # Write header lines if any
            for header in header_lines:
                f.write(header)
            
            if header_lines and sorted_keywords:
                f.write("\n")  # Add a blank line after headers if headers exist
            
            # Write sorted keywords
            for keyword in sorted_keywords:
                f.write(f"{keyword}\n")
        
        print(f"Sorted {len(sorted_keywords)} keywords from longest to shortest.")
        print(f"Saved to: {output_file_path}")
        return output_file_path
    
    except Exception as e:
        print(f"Error: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Sort keywords in a text file from longest to shortest.')
    parser.add_argument('input_file', help='Path to the input text file containing keywords')
    parser.add_argument('-o', '--output', help='Path to save the sorted keywords (optional)')
    args = parser.parse_args()
    
    sort_keywords_by_length(args.input_file, args.output)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # If no arguments provided, show a simple interactive prompt
        print("Keyword Sorter - Sort keywords from longest to shortest")
        print("========================================================")
        input_file = input("Enter path to the input text file: ")
        output_file = input("Enter path to the output file (leave blank for auto-generated filename): ").strip()
        
        if not output_file:
            output_file = None
            
        sort_keywords_by_length(input_file, output_file)
    else:
        main() 