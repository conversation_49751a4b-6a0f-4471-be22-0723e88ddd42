import time
import os
import datetime
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here
URL = "https://www.walmart.com/browse/patio-furniture/5428_91416?povid=HDL_PatioGarden_5428_POVcards_Outdoorupgrades_Patiofurniture_DSK_Jan_21"

# === USER: To use your real Chrome profile, set this to your profile path (e.g. '/home/<USER>/.config/google-chrome') ===
USE_REAL_PROFILE = False  # Set to True to use your real Chrome profile
CHROME_PROFILE_PATH = "/path/to/your/chrome/profile"  # Update this if USE_REAL_PROFILE is True

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        # Stealth options (only use supported experimental options)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        # Use real profile if enabled
        if USE_REAL_PROFILE:
            options.add_argument(f"--user-data-dir={CHROME_PROFILE_PATH}")
            print(f"[INFO] Using real Chrome profile at: {CHROME_PROFILE_PATH}")
        chrome_version = 135  # Adjust if your Chrome version changes
        driver = uc.Chrome(version_main=chrome_version, options=options)
        driver.get(url)
        print("If you see a CAPTCHA, please solve it in the browser window. Wait until you see products load before pressing Enter here.")
        input("Press Enter here to continue after solving any CAPTCHA...")
        output_dir = 'wallmart'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"wallmart_{timestamp}.txt")
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Walmart - Keywords\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        print(f"Created file for storing keywords: {os.path.abspath(filename)}")
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        while current_page <= max_pages and not last_page_reached:
            print(f"\n==== Scraping page {current_page} ====")
            page_results = process_page(driver.page_source)
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            # Try to click the next page button
            try:
                next_button = None
                try:
                    next_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'button.paginator-btn-next, a.paginator-btn-next'))
                    )
                except TimeoutException:
                    print("No more pagination buttons found. Reached the last page.")
                    last_page_reached = True
                    break
                if next_button:
                    print("Clicking next pagination button...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                    time.sleep(1)
                    try:
                        next_button.click()
                    except ElementClickInterceptedException:
                        print("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_button)
                    time.sleep(3)
                    current_page += 1
                else:
                    print("No next pagination button found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                print(f"Error during pagination: {e}")
                last_page_reached = True
                break
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All keywords have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nAll Product Titles:")
            for i, title in enumerate(all_results, 1):
                print(f"{i}. {title}")
            print(f"\nTotal products found: {len(all_results)}")
        input("\nPress Enter to close the browser and finish the script...")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver and input("Do you want to keep the browser open? (y/n): ").lower() != 'y':
            driver.quit()

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    # Try Walmart's typical product title selectors
    # 1. <a class="product-title-link">
    product_links = soup.find_all('a', class_=lambda x: x and 'product-title-link' in x)
    for link in product_links:
        title_text = link.get_text(strip=True)
        if title_text:
            results.append(title_text)
    # 2. <span class="lh-title"> (sometimes used)
    if not results:
        span_titles = soup.find_all('span', class_=lambda x: x and 'lh-title' in x)
        for span in span_titles:
            title_text = span.get_text(strip=True)
            if title_text:
                results.append(title_text)
    if not results:
        print("No product titles found with Walmart selectors.")
    return results

def main():
    selenium_crawl(URL, max_pages=20)

if __name__ == "__main__":
    main() 