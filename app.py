from flask import Flask, render_template_string, request
import requests
from bs4 import Beautiful<PERSON>oup
import openai
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException
import time

app = Flask(__name__)

# Simple HTML template
HTML_TEMPLATE = '''
<!doctype html>
<title>Crawl AI</title>
<h1>Crawl a Website with AI</h1>
<form method=post>
  <input type=text name=url placeholder="Paste website URL here" style="width: 300px;">
  <input type=submit value=Crawl>
</form>
{% if result %}
  <h2>Result:</h2>
  <pre>{{ result }}</pre>
{% endif %}
'''

# Dummy AI function (replace with real OpenAI logic as needed)
def ai_process(text):
    # Example: summarize the text (placeholder)
    return text[:1000] + ('...' if len(text) > 1000 else '')

def selenium_crawl(url):
    options = Options()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    try:
        driver = webdriver.Chrome(options=options)
        driver.get(url)
        time.sleep(3)  # Wait for JS to load
        html = driver.page_source
        driver.quit()
        return html
    except WebDriverException as e:
        return None

@app.route('/', methods=['GET', 'POST'])
def index():
    result = None
    if request.method == 'POST':
        url = request.form.get('url')
        try:
            html = selenium_crawl(url)
            if not html:
                resp = requests.get(url, timeout=10)
                html = resp.text
            soup = BeautifulSoup(html, 'html.parser')
            text = soup.get_text(separator=' ', strip=True)
            result = ai_process(text)
        except Exception as e:
            result = f"Error: {e}"
    return render_template_string(HTML_TEMPLATE, result=result)

if __name__ == '__main__':
    app.run(debug=True) 