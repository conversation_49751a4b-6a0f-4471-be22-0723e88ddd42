#!/usr/bin/env python3
import os
import random
import argparse
import glob

def get_all_txt_files(directory):
    """Get all .txt files in the given directory and its subdirectories."""
    txt_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.txt'):
                txt_files.append(os.path.join(root, file))
    return txt_files

def extract_keywords_from_file(file_path):
    """Extract keywords from a given text file."""
    keywords = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                # Assuming each line contains a keyword or phrase
                line = line.strip()
                if line:  # Skip empty lines
                    keywords.append(line)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    return keywords

def get_random_keywords(keywords, count=50):
    """Get a specified number of random keywords from the list."""
    if len(keywords) <= count:
        return keywords
    return random.sample(keywords, count)

def main():
    parser = argparse.ArgumentParser(description='Generate random keywords from text files')
    parser.add_argument('-d', '--directory', default='./houzz', help='Directory to search for text files (default: ./houzz)')
    parser.add_argument('-f', '--file', help='Specific text file to read (optional)')
    parser.add_argument('-c', '--count', type=int, default=50, help='Number of random keywords to output (default: 50)')
    parser.add_argument('-o', '--output', help='Output file to save keywords (optional)')
    args = parser.parse_args()
    
    all_keywords = []
    
    if args.file:
        # If a specific file is provided
        if os.path.exists(args.file) and args.file.endswith('.txt'):
            all_keywords = extract_keywords_from_file(args.file)
        else:
            print(f"File not found or not a text file: {args.file}")
            return
    else:
        # Otherwise, read all .txt files in the directory
        txt_files = get_all_txt_files(args.directory)
        print(f"Found {len(txt_files)} text files in {args.directory}")
        
        for file_path in txt_files:
            file_keywords = extract_keywords_from_file(file_path)
            all_keywords.extend(file_keywords)
            print(f"Read {len(file_keywords)} keywords from {file_path}")
    
    # Remove duplicates
    all_keywords = list(set(all_keywords))
    print(f"Total unique keywords found: {len(all_keywords)}")
    
    if not all_keywords:
        print("No keywords found!")
        return
    
    # Get random keywords
    random_keywords = get_random_keywords(all_keywords, args.count)
    
    # Output
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                for keyword in random_keywords:
                    f.write(f"{keyword}\n")
            print(f"Saved {len(random_keywords)} random keywords to {args.output}")
        except Exception as e:
            print(f"Error writing to {args.output}: {e}")
    else:
        print(f"\n{args.count} Random Keywords:\n")
        for keyword in random_keywords:
            print(keyword)

if __name__ == "__main__":
    main()