import time
import os
import datetime
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import random

# Set your target URL here
URL = "https://www.lowes.com/pl/bathroom-pedestal-sinks/bathroom-sinks/4294737299"

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        # --- Anti-bot evasion settings ---
        # Use a realistic user-agent
        user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/123.0.0.0 Safari/537.36"
        )
        options.add_argument(f'user-agent={user_agent}')
        # Use a fresh Chrome profile each run
        options.add_argument(f'--user-data-dir=/tmp/chrome_profile_{random.randint(10000,99999)}')
        # Set window size and language
        options.add_argument('window-size=1200,900')
        options.add_argument('--lang=en-US,en')
        # Do NOT use headless mode
        # options.add_argument('--headless')  # (commented out)
        # Disable automation flags (removed for compatibility)
        # options.add_experimental_option('excludeSwitches', ['enable-automation'])
        # options.add_experimental_option('useAutomationExtension', False)
        # --- End anti-bot evasion settings ---
        driver = uc.Chrome(options=options)
        driver.get(url)
        
        input("Please solve the CAPTCHA in the browser, then press Enter here to continue...")
        
        # Create output directory if it doesn't exist
        output_dir = 'lowes'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"lowes_pedestal_sinks_{timestamp}.txt")
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Lowe's Bathroom Pedestal Sinks - Product Titles\n")
            f.write(f"# Source: {URL}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing product titles: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        offset = 0
        
        while current_page <= max_pages and not last_page_reached:
            print(f"\n==== Scraping page {current_page} (offset={offset}) ====")
            page_results = process_page(driver.page_source)
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Try to go to the next page using offset
            next_page = current_page + 1
            offset += 24
            next_url = url.split('?')[0] + f'?offset={offset}'
            try:
                try:
                    close_overlays(driver)
                except Exception as e:
                    print(f"Note: Error handling overlay: {e}")
                # --- Add random delay to mimic human browsing ---
                delay = random.uniform(4, 10)
                print(f"Sleeping for {delay:.1f} seconds before next page...")
                time.sleep(delay)
                print(f"Navigating to next page: {next_url}")
                driver.get(next_url)
                # --- Add another random delay after page load ---
                delay = random.uniform(4, 10)
                print(f"Sleeping for {delay:.1f} seconds after page load...")
                time.sleep(delay)
                current_page = next_page
            except Exception as e:
                print(f"Error during pagination: {e}")
                last_page_reached = True
                break
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All product titles have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nAll Product Titles:")
            for i, title in enumerate(all_results, 1):
                print(f"{i}. {title}")
            print(f"\nTotal products found: {len(all_results)}")
        input("\nPress Enter to close the browser and finish the script...")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver and input("Do you want to keep the browser open? (y/n): ").lower() != 'y':
            driver.quit()

def close_overlays(driver):
    close_buttons = driver.find_elements(By.XPATH, '//div[@aria-label="Close"]')
    if close_buttons:
        print(f"Found {len(close_buttons)} potential overlay(s). Attempting to close...")
        for button in close_buttons:
            try:
                button.click()
                print("Clicked a close button.")
                time.sleep(1)
            except Exception as e:
                print(f"Could not click close button: {e}")
                try:
                    driver.execute_script("arguments[0].click();", button)
                    print("Used JavaScript to click close button.")
                    time.sleep(1)
                except Exception as js_e:
                    print(f"JavaScript click also failed: {js_e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    # Find all product containers
    product_divs = soup.find_all('div', class_='brand-description')
    for div in product_divs:
        brand = div.find('span', {'data-selector': 'splp-prd-brd-nm'})
        desc = div.find('span', class_='description-spn')
        if brand and desc:
            title_text = f"{brand.get_text(strip=True)} {desc.get_text(strip=True)}"
            results.append(title_text)
        elif desc:
            results.append(desc.get_text(strip=True))
    if not results:
        print("No product titles found with <div class='brand-description'> selector.")
    return results

def main():
    selenium_crawl(URL, max_pages=20)

if __name__ == "__main__":
    main() 