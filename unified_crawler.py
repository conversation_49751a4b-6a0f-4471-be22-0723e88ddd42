from flask import Flask, render_template_string, request, redirect, url_for, jsonify, send_file
import threading
import time
import os
import datetime
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

app = Flask(__name__)

crawl_results = {
    'status': 'idle',
    'log': [],
    'products': [],
    'filename': ''
}

stop_flag = {'stop': False}

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Unified Crawler</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 700px; margin: auto; }
        .log { background: #f4f4f4; padding: 10px; border-radius: 5px; height: 200px; overflow-y: auto; }
        .status { font-weight: bold; }
        .success { color: green; }
        .error { color: red; }
        .keyword-list { background: #fffde7; padding: 10px; border-radius: 5px; margin-top: 20px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
<div class="container">
    <h1>Multi-Engine Product Keyword Crawler</h1>
    <form method="post" action="/crawl">
        <label for="engine">Select Crawler Engine:</label>
        <select id="engine" name="engine">
            <option value="livingspaces">Living Spaces</option>
            <option value="wayfair">Wayfair</option>
            <option value="worldmarket">World Market</option>
            <option value="belacor">Belacor</option>
        </select><br><br>
        <label for="url">Enter Category/Product URL:</label><br>
        <input type="text" id="url" name="url" style="width: 80%;" required value="{{ url }}"><br><br>
        <button type="submit">Start Crawl</button>
        <button type="button" onclick="stopCrawl()">Stop</button>
        <button type="button" onclick="exportKeywords()">Export Keywords</button>
    </form>
    <hr>
    <div>
        <span class="status">Status:</span> <span id="status">{{ status }}</span>
    </div>
    <div class="log" id="log">
        {% for line in log %}{{ line }}<br>{% endfor %}
    </div>
    <div class="keyword-list" id="keyword-list">
        <b>Keywords:</b>
        <ul>
        {% for p in products %}<li>{{ p }}</li>{% endfor %}
        </ul>
        {% if filename %}<p>Saved to: <b>{{ filename }}</b></p>{% endif %}
    </div>
</div>
<script>
function fetchStatus() {
    fetch('/status').then(r => r.json()).then(data => {
        document.getElementById('status').textContent = data.status;
        document.getElementById('log').innerHTML = data.log.map(l => l + '<br>').join('');
        let kwList = document.getElementById('keyword-list');
        if (kwList) {
            let html = '<b>Keywords:</b><ul>';
            for (let p of data.products) html += '<li>'+p+'</li>';
            html += '</ul>';
            if (data.filename) html += '<p>Saved to: <b>'+data.filename+'</b></p>';
            kwList.innerHTML = html;
        }
    });
}
function stopCrawl() {
    fetch('/stop', {method: 'POST'});
}
function exportKeywords() {
    window.location.href = '/export';
}
setInterval(fetchStatus, 2000);
</script>
</body>
</html>
'''

def log(line):
    crawl_results['log'].append(line)
    if len(crawl_results['log']) > 100:
        crawl_results['log'] = crawl_results['log'][-100:]

def crawl_livingspaces(url):
    crawl_results['status'] = 'running'
    crawl_results['log'] = []
    crawl_results['products'] = []
    crawl_results['filename'] = ''
    stop_flag['stop'] = False
    driver = None
    try:
        options = uc.ChromeOptions()
        driver = uc.Chrome(options=options)
        driver.get(url)
        log("If there is a CAPTCHA, please solve it in the browser window.")
        time.sleep(5)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"livingspaces_{timestamp}.txt"
        crawl_results['filename'] = filename
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Living Spaces - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        log(f"Created file for storing product titles: {os.path.abspath(filename)}")
        all_results = []
        current_page = 1
        last_page_reached = False
        while not last_page_reached:
            if stop_flag['stop']:
                log('Crawl stopped by user.')
                break
            log(f"==== Scraping page {current_page} ====")
            page_results = process_page_livingspaces(driver.page_source)
            if page_results:
                all_results.extend(page_results)
                crawl_results['products'] = list(all_results)
                log(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        f.write(f"{title}\n")
                log(f"Appended {len(page_results)} products to {filename}")
            else:
                log(f"No products found on page {current_page}")
            current_page += 1
            try:
                next_arrow = None
                try:
                    next_arrow = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'div.pagination-arrow:not(.pagination-arrow-disabled) .fa-angle-right'))
                    )
                except TimeoutException:
                    log("No more pagination arrows found. Reached the last page.")
                    last_page_reached = True
                    break
                if next_arrow:
                    log("Clicking next pagination arrow...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_arrow)
                    time.sleep(1)
                    try:
                        next_arrow.click()
                    except ElementClickInterceptedException:
                        log("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_arrow)
                    time.sleep(3)
                else:
                    log("No next pagination arrow found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                log(f"Error during pagination: {e}")
                last_page_reached = True
                break
        crawl_results['products'] = all_results
        crawl_results['status'] = 'done'
        log(f"Crawling complete. Scraped {current_page-1} pages with {len(all_results)} total products.")
        log(f"All product titles have been saved to: {os.path.abspath(filename)}")
    except WebDriverException as e:
        crawl_results['status'] = 'error'
        log(f"Selenium error: {e}")
    finally:
        if driver:
            driver.quit()

def process_page_livingspaces(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    product_containers = soup.find_all('div', class_='product-item-container')
    for container in product_containers:
        name_tag = container.find('h3', class_='name')
        if name_tag:
            title_text = name_tag.get_text(strip=True)
            if title_text:
                results.append(title_text)
    if not results:
        log("No product titles found with <div class='product-item-container'> selector.")
    return results

def crawl_wayfair(url):
    crawl_results['status'] = 'running'
    crawl_results['log'] = []
    crawl_results['products'] = []
    crawl_results['filename'] = ''
    stop_flag['stop'] = False
    driver = None
    try:
        options = uc.ChromeOptions()
        driver = uc.Chrome(options=options)
        driver.get(url)
        log("If there is a CAPTCHA, please solve it in the browser window.")
        time.sleep(5)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"wayfair_{timestamp}.txt"
        crawl_results['filename'] = filename
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Wayfair - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        log(f"Created file for storing product titles: {os.path.abspath(filename)}")
        all_results = []
        current_page = 1
        last_page_reached = False
        while not last_page_reached:
            if stop_flag['stop']:
                log('Crawl stopped by user.')
                break
            log(f"==== Scraping page {current_page} ====")
            page_results = process_page_wayfair(driver.page_source)
            if page_results:
                all_results.extend(page_results)
                crawl_results['products'] = list(all_results)
                log(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        f.write(f"{title}\n")
                log(f"Appended {len(page_results)} products to {filename}")
            else:
                log(f"No products found on page {current_page}")
            current_page += 1
            try:
                # Try to find pagination links
                pagination_link = None
                try:
                    pagination_link = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, '//a[contains(@aria-label, "Next") or contains(text(), "Next")]'))
                    )
                except TimeoutException:
                    log("No more pagination links found. Reached the last page.")
                    last_page_reached = True
                    break
                if pagination_link:
                    log("Clicking next pagination link...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", pagination_link)
                    time.sleep(1)
                    try:
                        pagination_link.click()
                    except ElementClickInterceptedException:
                        log("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", pagination_link)
                    time.sleep(3)
                else:
                    log("No next pagination link found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                log(f"Error during pagination: {e}")
                last_page_reached = True
                break
        crawl_results['products'] = all_results
        crawl_results['status'] = 'done'
        log(f"Crawling complete. Scraped {current_page-1} pages with {len(all_results)} total products.")
        log(f"All product titles have been saved to: {os.path.abspath(filename)}")
    except WebDriverException as e:
        crawl_results['status'] = 'error'
        log(f"Selenium error: {e}")
    finally:
        if driver:
            driver.quit()

def process_page_wayfair(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    # Find all h2 elements with the specified attributes and classes
    product_titles = soup.find_all('h2', {
        'data-hb-id': 'Text', 
        'data-test-id': 'ListingCard-ListingCardName-Text',
        'data-name-id': 'ListingCardName',
        'class': lambda x: x and ('_6o3atzbl' in x) and ('_6o3atz174' in x) and ('_1lxwj2q1' in x)
    })
    if not product_titles:
        product_titles = soup.find_all('h2')
    for title in product_titles:
        title_text = title.get_text(strip=True)
        results.append(title_text)
    return results

def crawl_worldmarket(url):
    crawl_results['status'] = 'running'
    crawl_results['log'] = []
    crawl_results['products'] = []
    crawl_results['filename'] = ''
    stop_flag['stop'] = False
    driver = None
    try:
        options = uc.ChromeOptions()
        driver = uc.Chrome(options=options)
        driver.get(url)
        log("If there is a CAPTCHA, please solve it in the browser window.")
        time.sleep(5)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"worldmarket_{timestamp}.txt"
        crawl_results['filename'] = filename
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# World Market - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        log(f"Created file for storing product titles: {os.path.abspath(filename)}")
        all_results = []
        current_page = 1
        last_page_reached = False
        while not last_page_reached:
            if stop_flag['stop']:
                log('Crawl stopped by user.')
                break
            log(f"==== Scraping page {current_page} ====")
            page_results = process_page_worldmarket(driver.page_source)
            if page_results:
                all_results.extend(page_results)
                crawl_results['products'] = list(all_results)
                log(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        f.write(f"{title}\n")
                log(f"Appended {len(page_results)} products to {filename}")
            else:
                log(f"No products found on page {current_page}")
            current_page += 1
            try:
                next_arrow = None
                try:
                    next_arrow = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'a.js-pagination-arrow.pagination-arrow-button:not(.disabled):not(.prev)'))
                    )
                except TimeoutException:
                    log("No more pagination arrows found. Reached the last page.")
                    last_page_reached = True
                    break
                if next_arrow:
                    log("Clicking next pagination arrow...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_arrow)
                    time.sleep(1)
                    try:
                        next_arrow.click()
                    except ElementClickInterceptedException:
                        log("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_arrow)
                    time.sleep(3)
                else:
                    log("No next pagination arrow found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                log(f"Error during pagination: {e}")
                last_page_reached = True
                break
        crawl_results['products'] = all_results
        crawl_results['status'] = 'done'
        log(f"Crawling complete. Scraped {current_page-1} pages with {len(all_results)} total products.")
        log(f"All product titles have been saved to: {os.path.abspath(filename)}")
    except WebDriverException as e:
        crawl_results['status'] = 'error'
        log(f"Selenium error: {e}")
    finally:
        if driver:
            driver.quit()

def process_page_worldmarket(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    product_links = soup.find_all('a', class_=lambda x: x and 'link' in x and 'js-a-product-click' in x)
    for link in product_links:
        title_text = link.get_text(strip=True)
        if title_text:
            results.append(title_text)
    if not results:
        log("No product titles found with <a class='link js-a-product-click'> selector.")
    return results

def crawl_belacor(url):
    crawl_results['status'] = 'running'
    crawl_results['log'] = []
    crawl_results['products'] = []
    crawl_results['filename'] = ''
    stop_flag['stop'] = False
    driver = None
    try:
        options = uc.ChromeOptions()
        driver = uc.Chrome(options=options)
        driver.get(url)
        log("If there is a CAPTCHA, please solve it in the browser window.")
        time.sleep(5)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"belacor_{timestamp}.txt"
        crawl_results['filename'] = filename
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Belacor - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        log(f"Created file for storing product titles: {os.path.abspath(filename)}")
        all_results = []
        current_page = 1
        last_page_reached = False
        while not last_page_reached:
            if stop_flag['stop']:
                log('Crawl stopped by user.')
                break
            log(f"==== Scraping page {current_page} ====")
            page_results = process_page_belacor(driver.page_source)
            if page_results:
                all_results.extend(page_results)
                crawl_results['products'] = list(all_results)
                log(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        f.write(f"{title}\n")
                log(f"Appended {len(page_results)} products to {filename}")
            else:
                log(f"No products found on page {current_page}")
            current_page += 1
            try:
                soup = BeautifulSoup(driver.page_source, 'html.parser')
                next_page_link = None
                for a in soup.find_all('a', class_=lambda x: x and 'cl-pagination__link' in x):
                    if a.get_text(strip=True) == str(current_page):
                        next_page_link = a.get('href')
                        break
                if next_page_link:
                    log(f"Navigating to next page: {next_page_link}")
                    driver.get(next_page_link)
                    time.sleep(3)
                else:
                    log("No more pagination links found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                log(f"Error during pagination: {e}")
                last_page_reached = True
                break
        crawl_results['products'] = all_results
        crawl_results['status'] = 'done'
        log(f"Crawling complete. Scraped {current_page-1} pages with {len(all_results)} total products.")
        log(f"All product titles have been saved to: {os.path.abspath(filename)}")
    except WebDriverException as e:
        crawl_results['status'] = 'error'
        log(f"Selenium error: {e}")
    finally:
        if driver:
            driver.quit()

def process_page_belacor(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    product_anchors = soup.find_all('a', class_=lambda x: x and 'product-tile__name' in x)
    if not product_anchors:
        log("No product anchors found with specific selector.")
    for anchor in product_anchors:
        ellip_span = anchor.find('span', class_='ellip')
        if ellip_span:
            words = [w.get_text(strip=True) for w in ellip_span.find_all('span')]
            title_text = ' '.join(words)
            results.append(title_text)
    return results

@app.route('/', methods=['GET'])
def index():
    return render_template_string(HTML_TEMPLATE, url='', status=crawl_results['status'], log=crawl_results['log'], products=crawl_results['products'], filename=crawl_results['filename'])

@app.route('/crawl', methods=['POST'])
def crawl():
    url = request.form['url']
    engine = request.form['engine']
    crawl_results['status'] = 'starting'
    crawl_results['log'] = []
    crawl_results['products'] = []
    crawl_results['filename'] = ''
    stop_flag['stop'] = False
    if engine == 'livingspaces':
        t = threading.Thread(target=crawl_livingspaces, args=(url,))
    elif engine == 'wayfair':
        t = threading.Thread(target=crawl_wayfair, args=(url,))
    elif engine == 'worldmarket':
        t = threading.Thread(target=crawl_worldmarket, args=(url,))
    elif engine == 'belacor':
        t = threading.Thread(target=crawl_belacor, args=(url,))
    else:
        crawl_results['status'] = 'error'
        log('Unknown engine selected.')
        return redirect(url_for('index'))
    t.start()
    return redirect(url_for('index'))

@app.route('/stop', methods=['POST'])
def stop():
    stop_flag['stop'] = True
    crawl_results['status'] = 'stopping'
    log('Stop requested by user.')
    return ('', 204)

@app.route('/status')
def status():
    return jsonify({
        'status': crawl_results['status'],
        'log': crawl_results['log'],
        'products': crawl_results['products'],
        'filename': crawl_results['filename']
    })

@app.route('/export')
def export():
    if crawl_results['filename'] and os.path.exists(crawl_results['filename']):
        return send_file(crawl_results['filename'], as_attachment=True)
    else:
        return 'No file to export', 404

if __name__ == "__main__":
    app.run(debug=True, port=5000) 