#!/usr/bin/env python3
import time
import os
import datetime
import random
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here
URL = "https://www.houzz.com/products/outdoor-fountains"

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        # --- Anti-bot evasion settings ---
        user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/123.0.0.0 Safari/537.36"
        )
        options.add_argument(f'user-agent={user_agent}')
        options.add_argument(f'--user-data-dir=/tmp/chrome_profile_{random.randint(10000,99999)}')
        options.add_argument('window-size=1200,900')
        options.add_argument('--lang=en-US,en')
        options.add_argument('--disable-blink-features=AutomationControlled')
        # Do NOT use headless mode
        # --- End anti-bot evasion settings ---
        chrome_version = 135  # Update this to match your Chrome version
        driver = uc.Chrome(version_main=chrome_version, options=options)
        
        driver.get(url)
        time.sleep(random.uniform(2, 4))
        
        # Accept cookies if the dialog appears
        try:
            cookie_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Accept")]'))
            )
            cookie_button.click()
            print("Accepted cookies")
            time.sleep(random.uniform(1, 2))
        except TimeoutException:
            print("No cookie dialog found or it was already accepted")
        
        # Allow some time for the page to fully load
        print("Waiting for page to load completely...")
        time.sleep(5)
        
        # Try to close any overlays/popups that might appear
        close_overlays(driver)
        time.sleep(random.uniform(1, 2))
        
        # Create output directory if it doesn't exist
        output_dir = 'americanfurniture'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"americanfurniture_{timestamp}.txt")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# American Home Furniture - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing product titles: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        
        while current_page <= max_pages and not last_page_reached:
            print(f"\n==== Scraping page {current_page} ====")
            
            # Scroll down slowly to load all products
            for i in range(10):
                driver.execute_script(f"window.scrollTo(0, {i * 500});")
                time.sleep(random.uniform(0.2, 0.5))
            
            page_results = process_page(driver.page_source)
            time.sleep(random.uniform(1, 2))
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Use Selenium to click the next pagination link if present
            try:
                next_button = None
                try:
                    # Using the pagination structure provided
                    next_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, 'span.next a'))
                    )
                except TimeoutException:
                    # Try alternative selector
                    try:
                        next_button = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, 'a[title="Next"]'))
                        )
                    except TimeoutException:
                        print("No next page button found. Reached the last page.")
                        last_page_reached = True
                        break
                
                if next_button:
                    print("Clicking next pagination button...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                    time.sleep(random.uniform(1, 2))
                    try:
                        next_button.click()
                    except ElementClickInterceptedException:
                        print("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_button)
                    
                    # Wait longer after pagination to ensure the new page loads
                    print("Waiting for next page to load...")
                    time.sleep(random.uniform(3, 5))
                    
                    # Scroll back to top of page
                    driver.execute_script("window.scrollTo(0, 0);")
                    
                    # Additional wait for elements to load after scrolling back up
                    time.sleep(1)
                    
                    current_page += 1
                else:
                    print("No next pagination button found. Reached the last page.")
                    last_page_reached = True
                    break
            except Exception as e:
                print(f"Error during pagination: {e}")
                last_page_reached = True
                break
                
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All product titles have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nSample Product Titles:")
            for i, title in enumerate(all_results[:10], 1):
                print(f"{i}. {title}")
            if len(all_results) > 10:
                print(f"... and {len(all_results) - 10} more")
            print(f"\nTotal products found: {len(all_results)}")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver:
            print("Closing browser...")
            driver.quit()

def close_overlays(driver):
    # Try to close various types of overlays common on e-commerce sites
    selectors = [
        '//button[@aria-label="Close"]',
        '//div[@aria-label="Close"]',
        '//button[contains(text(), "Close")]',
        '//button[contains(@class, "close")]',
        '//span[contains(@class, "close")]',
        '//div[contains(@class, "popup-close")]',
        '//button[contains(text(), "No thanks")]',
        '//a[contains(text(), "Close")]'
    ]
    
    for selector in selectors:
        try:
            close_buttons = driver.find_elements(By.XPATH, selector)
            if close_buttons:
                print(f"Found {len(close_buttons)} potential overlay(s) with selector {selector}. Attempting to close...")
                for button in close_buttons:
                    try:
                        button.click()
                        print("Clicked a close button.")
                        time.sleep(random.uniform(0.5, 1))
                    except Exception as e:
                        print(f"Could not click close button: {e}")
                        try:
                            driver.execute_script("arguments[0].click();", button)
                            print("Used JavaScript to click close button.")
                            time.sleep(random.uniform(0.5, 1))
                        except Exception as js_e:
                            print(f"JavaScript click also failed: {js_e}")
        except Exception as e:
            print(f"Error finding overlay close buttons with selector {selector}: {e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    
    # Look for products with the exact selector provided by user
    product_titles = soup.find_all('div', class_='grid-product__title')
    
    if product_titles:
        print(f"Found {len(product_titles)} products using div.grid-product__title selector")
        for title_elem in product_titles:
            try:
                # Get the product title text and normalize whitespace
                title_text = title_elem.get_text(strip=True)
                # Replace multiple spaces and newlines with a single space
                title = ' '.join(title_text.split())
                
                if title:
                    results.append(title)
            except Exception as e:
                print(f"Error extracting product info: {e}")
        
        return results
    
    # If the primary selector didn't work, try alternative selectors
    print("No products found with div.grid-product__title selector. Trying alternative selectors...")
    
    # Try other common product title selectors
    alternative_selectors = [
        ('h3', 'product-title'),
        ('div', 'product-item__title'),
        ('h2', 'product-name'),
        ('div', 'product-name'),
        ('a', 'product-title-link')
    ]
    
    for tag, class_name in alternative_selectors:
        product_titles = soup.find_all(tag, class_=class_name)
        if product_titles:
            print(f"Found {len(product_titles)} products using {tag}.{class_name} selector")
            for title_elem in product_titles:
                try:
                    # Get the product title text and normalize whitespace
                    title_text = title_elem.get_text(strip=True)
                    # Replace multiple spaces and newlines with a single space
                    title = ' '.join(title_text.split())
                    
                    if title:
                        results.append(title)
                except Exception as e:
                    print(f"Error extracting product info: {e}")
            break
    
    if not results:
        print("No products found with any of the selectors. The site structure might have changed.")
        print("Dumping some HTML to help debug:")
        # Print a small section of the HTML to help debug
        print(soup.prettify()[:1000])
    
    return results

def main():
    print("American Home Furniture Product Crawler")
    custom_url = URL
    max_pages = 10
    
    print(f"\nStarting crawler for: {custom_url}")
    print(f"Will crawl up to {max_pages} pages\n")
    
    # Ensure output directory exists
    os.makedirs('americanfurniture', exist_ok=True)
    print(f"American Home Furniture directory created/verified at: {os.path.abspath('americanfurniture')}")
    
    selenium_crawl(custom_url, max_pages=max_pages)

if __name__ == "__main__":
    main() 