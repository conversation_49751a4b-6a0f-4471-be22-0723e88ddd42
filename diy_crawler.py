#!/usr/bin/env python3
import time
import random
import argparse
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Default URLs to crawl
DEFAULT_URLS = [
    "https://www.diy.com/outdoor-garden/garden-furniture/garden-furniture-sets.cat"
]

class DIYCrawler:
    def __init__(self, urls, output_file=None, output_dir='diy', delay_range=(0.5, 1)):
        self.urls = urls if isinstance(urls, list) else [urls]
        self.delay_range = delay_range
        self.output_dir = output_dir
        self.output_files = []  # List to store all output files
        
        # Setup Selenium
        self.setup_selenium()
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Create a separate output file for each URL
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for i, url in enumerate(self.urls):
            # Extract a name from the URL for the filename
            url_parts = url.split('/')
            product_type = url_parts[-1].replace('.cat', '') if url_parts[-1] else f"product_{i+1}"
            
            # Create output filename
            if output_file is None:
                file_name = f"{output_dir}/{product_type}_{timestamp}.txt"
            else:
                base, ext = os.path.splitext(output_file)
                file_name = f"{output_dir}/{base}_{product_type}{ext}"
                
            self.output_files.append(file_name)
            
            # Create or clear the output file
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write("# DIY.com Product Titles\n")
                f.write(f"# Source: {url}\n")
                f.write(f"# Scraped: {datetime.now().strftime('%Y-%m-%d')}\n\n")
                
            print(f"Created file for storing product titles from {product_type}: {os.path.abspath(file_name)}")

    def setup_selenium(self):
        """Set up the Selenium WebDriver with optimized settings"""
        # Configure Chrome options
        options = Options()
        
        # Performance optimizations
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-infobars")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-popup-blocking")
        # make it headless
        options.add_argument("--headless")
        
        # Anti-detection measures
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--start-maximized")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        
        # Set a realistic user agent
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        
        # Create the WebDriver
        self.driver = webdriver.Chrome(options=options)
        
        # Add undetected settings
        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
            """
        })
        
        # Set shorter implicit wait time
        self.driver.implicitly_wait(5)

    def get_page(self, url):
        """Navigate to the URL and get page content"""
        try:
            print(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Reduce delay
            time.sleep(0.5)
            
            # Wait for the product list to load
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="product-list"]'))
                )
            except TimeoutException:
                print("Product list took too long to load, continuing anyway...")
            
            return True
        except Exception as e:
            print(f"Error fetching {url}: {e}")
            return False

    def get_product_titles(self):
        """Extract product titles using DIY.com specific selectors"""
        try:
            products = []
            
            # Primary selector for DIY.com product names
            selectors = [
                'p[data-testid="product-name"]',
                '.mb-sm.font-bold[data-testid="product-name"]',
                '[data-testid="product-name"]'
            ]
            
            # Try each selector
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"Found {len(elements)} products using selector: {selector}")
                        for elem in elements:
                            title = elem.text.strip()
                            
                            # Simple validation - ensure it's not empty and unique
                            if title and len(title) > 3 and title not in products:
                                products.append(title)
                        
                        # If we found products with this selector, don't try others
                        if products:
                            break
                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue
            
            # If primary selectors fail, try a fallback approach
            if not products:
                print("Primary selectors failed, trying fallback...")
                # Look for product containers and extract titles from within
                product_containers = self.driver.find_elements(By.CSS_SELECTOR, '[data-testid="product"]')
                for container in product_containers[:50]:  # Limit to first 50 for speed
                    try:
                        # Try to find a title element within this container
                        title_elem = container.find_element(By.CSS_SELECTOR, 'p.font-bold, .product-name, h3, h4')
                        title = title_elem.text.strip()
                        if title and len(title) > 3 and title not in products:
                            products.append(title)
                    except:
                        continue
            
            print(f"Found {len(products)} product titles")
            return products
        
        except Exception as e:
            print(f"Error extracting product titles: {e}")
            return []

    def click_load_more(self):
        """Click the 'Load more' button to get more products"""
        try:
            # Look for the "Load more" button with various selectors
            load_more_selectors = [
                'a[data-testid="next-page"]',
                'a:contains("Load more")',
                '.button:contains("Load more")',
                '[href*="page="]'
            ]
            
            for selector in load_more_selectors:
                try:
                    if 'contains' in selector:
                        # Use XPath for text-based search
                        xpath_selector = f"//a[contains(text(), 'Load more')] | //button[contains(text(), 'Load more')]"
                        elements = self.driver.find_elements(By.XPATH, xpath_selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        element = elements[0]
                        # Check if element is clickable
                        if element.is_displayed() and element.is_enabled():
                            print(f"Found 'Load more' button, clicking...")
                            
                            # Scroll to the button to ensure it's visible
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            time.sleep(0.5)
                            
                            # Click the button
                            element.click()
                            
                            # Wait for new content to load
                            time.sleep(2)
                            return True
                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue
            
            print("No 'Load more' button found or clickable")
            return False
            
        except Exception as e:
            print(f"Error clicking load more button: {e}")
            return False

    def save_titles(self, titles, output_file):
        """Append titles to the output file and show sample in console"""
        if not titles:
            return
            
        with open(output_file, 'a', encoding='utf-8') as f:
            for title in titles:
                f.write(f"{title}\n")
                
        # Print sample of titles for verification
        print("\nSample titles from this batch:")
        for i, title in enumerate(titles[:3], 1):
            print(f"  {i}. {title}")
        if len(titles) > 3:
            print(f"  ... and {len(titles) - 3} more")

    def sort_output_file(self, output_file):
        """Sort the product titles in the output file alphabetically"""
        try:
            # Skip the header (first 4 lines)
            with open(output_file, 'r', encoding='utf-8') as f:
                header_lines = [f.readline() for _ in range(4)]
                product_lines = [line.strip() for line in f if line.strip()]
            
            # Sort the product lines
            product_lines.sort()
            
            # Create a new sorted filename
            base_name = os.path.splitext(output_file)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            sorted_file = f"{base_name}_sorted_{timestamp}.txt"
            
            # Write the sorted file
            with open(sorted_file, 'w', encoding='utf-8') as f:
                f.writelines(line + '\n' for line in header_lines)
                f.writelines(line + '\n' for line in product_lines)
                
            print(f"\nCreated sorted output file: {os.path.abspath(sorted_file)}")
            
            # Delete the original file
            os.remove(output_file)
            print(f"Original file deleted: {os.path.abspath(output_file)}")
            
            return sorted_file
        except Exception as e:
            print(f"Error sorting output file: {e}")
            return None

    def crawl_all_urls(self, max_loads=None):
        """Crawl all URLs in the list"""
        total_loads = 0
        total_products = 0
        sorted_files = []
        
        try:
            for i, url in enumerate(self.urls, 1):
                print(f"\n\n{'=' * 50}")
                print(f"Processing URL {i}/{len(self.urls)}: {url}")
                print(f"{'=' * 50}\n")
                
                # Get the corresponding output file for this URL
                output_file = self.output_files[i-1]
                
                # Crawl this URL
                loads, products = self.crawl_single_url(url, output_file, max_loads)
                
                total_loads += loads
                total_products += products
                
                # Add some delay between processing different URLs
                if i < len(self.urls):
                    delay = random.uniform(1.0, 2.0)  # Slightly longer delay between URLs
                    print(f"\nWaiting {delay:.1f} seconds before processing next URL...")
                    time.sleep(delay)
        
        finally:
            # Clean up resources
            print("\nClosing browser and cleaning up resources...")
            try:
                self.driver.quit()
            except Exception as e:
                print(f"Error closing browser: {e}")
            
            print(f"\n==== Crawl completed for all URLs ====")
            print(f"Processed {len(self.urls)} URLs, {total_loads} load operations with {total_products} total products.")
            print(f"Results saved to files:")
            for file in self.output_files:
                print(f"- {os.path.abspath(file)}")
    
    def crawl_single_url(self, base_url, output_file, max_loads=None):
        """Crawl a single URL and handle 'Load more' pagination"""
        try:
            load_count = 0
            product_count = 0
            consecutive_empty_loads = 0
            max_empty_loads = 3  # Stop after this many consecutive loads with no new products
            
            print(f"Starting crawl at {base_url}")
            print(f"Saving results to: {output_file}")
            
            # Navigate to the initial page
            success = self.get_page(base_url)
            if not success:
                print("Failed to load initial page. Stopping crawl.")
                return 0, 0
            
            # Track products we've already seen to avoid duplicates
            all_seen_products = set()
            
            while True:
                load_count += 1
                print(f"\n==== Load #{load_count} ====")
                
                # Check if we've reached the max loads limit
                if max_loads and load_count > max_loads:
                    print(f"Reached maximum load limit ({max_loads})")
                    break
                
                # Extract product titles from current page state
                titles = self.get_product_titles()
                
                # Filter out products we've already seen
                new_titles = [title for title in titles if title not in all_seen_products]
                new_count = len(new_titles)
                
                # Add new titles to our tracking set
                all_seen_products.update(new_titles)
                
                product_count += new_count
                
                print(f"Found {len(titles)} total products, {new_count} new products on this load. Total unique so far: {len(all_seen_products)}")
                
                # Check for consecutive empty loads
                if new_count == 0:
                    consecutive_empty_loads += 1
                    if consecutive_empty_loads >= max_empty_loads:
                        print(f"Reached {max_empty_loads} consecutive loads with no new products. Stopping crawl.")
                        break
                else:
                    consecutive_empty_loads = 0  # Reset counter when we find new products
                
                # Save only the new titles to avoid duplicates in output
                self.save_titles(new_titles, output_file)
                
                # Try to click "Load more" button
                more_loaded = self.click_load_more()
                
                if not more_loaded:
                    print("No more content to load. End of crawl.")
                    break
                
                # Add a delay between load operations
                delay = random.uniform(self.delay_range[0], self.delay_range[1])
                print(f"Waiting {delay:.1f} seconds before next load...")
                time.sleep(delay)
            
            return load_count, len(all_seen_products)
        
        except Exception as e:
            print(f"Error crawling {base_url}: {e}")
            return 0, 0

def main():
    parser = argparse.ArgumentParser(description='DIY.com product listings crawler')
    parser.add_argument('--urls', type=str, nargs='+', default=DEFAULT_URLS,
                        help='List of URLs to crawl')
    parser.add_argument('--url-file', type=str, default=None,
                        help='Path to a text file with one URL per line')
    parser.add_argument('--output', type=str, default=None,
                        help='Base output file name (will be modified for each URL)')
    parser.add_argument('--max-loads', type=int, default=None,
                        help='Maximum number of "Load more" operations per URL')
    parser.add_argument('--output-dir', type=str, default='diy',
                        help='Directory to store output files')
    parser.add_argument('--min-delay', type=float, default=0.5,
                        help='Minimum delay between requests in seconds')
    parser.add_argument('--max-delay', type=float, default=1.0,
                        help='Maximum delay between requests in seconds')
    
    args = parser.parse_args()
    
    # Collect URLs from both arguments and file (if provided)
    urls = args.urls
    
    # If a URL file is provided, read URLs from it
    if args.url_file:
        try:
            with open(args.url_file, 'r') as f:
                file_urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                urls.extend(file_urls)
                print(f"Loaded {len(file_urls)} URLs from {args.url_file}")
        except Exception as e:
            print(f"Error reading URL file: {e}")
    
    # Remove any duplicate URLs
    urls = list(dict.fromkeys(urls))
    
    # Print some startup info
    print("DIY.com Product Crawler")
    print(f"Number of URLs to process: {len(urls)}")
    print("URLs to crawl:")
    for i, url in enumerate(urls, 1):
        print(f"  {i}. {url}")
    
    if args.max_loads:
        print(f"Will perform up to {args.max_loads} 'Load more' operations per URL")
    print(f"Output directory: {os.path.abspath(args.output_dir)}")
    print(f"Delay between requests: {args.min_delay}-{args.max_delay} seconds")
    print(f"Each URL will be saved to a separate output file")
    print()
    
    # Start the crawler
    crawler = DIYCrawler(
        urls, 
        args.output, 
        args.output_dir,
        delay_range=(args.min_delay, args.max_delay)
    )
    crawler.crawl_all_urls(args.max_loads)

if __name__ == "__main__":
    main() 