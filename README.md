# Crawl AI Flask App

This is a simple Flask web app that lets you crawl a website by pasting its URL. The app fetches the page, extracts the text, and processes it with a placeholder AI function (ready for OpenAI integration).

## Features

- Paste a website URL and crawl its content
- Extracts and displays the main text
- Ready for AI-powered processing (OpenAI integration placeholder)

## Setup

1. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

2. **Run the app:**

   ```bash
   python app.py
   ```

3. **Open in your browser:**

   Go to [http://127.0.0.1:5000](http://127.0.0.1:5000)

## Customizing AI Logic

- The `ai_process` function in `app.py` is a placeholder. Replace it with your OpenAI logic as needed.

---

**Note:** For OpenAI integration, set your API key in the environment or directly in the code as needed.
