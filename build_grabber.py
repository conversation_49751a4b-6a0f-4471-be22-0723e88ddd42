#!/usr/bin/env python3
import time
import os
import datetime
import random
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here
URL = "https://www.build.com/shop-all-flooring/c82044042"

def selenium_crawl(url, max_pages=10):
    driver = None
    try:
        options = uc.ChromeOptions()
        # --- Anti-bot evasion settings ---
        user_agent = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/123.0.0.0 Safari/537.36"
        )
        options.add_argument(f'user-agent={user_agent}')
        options.add_argument(f'--user-data-dir=/tmp/chrome_profile_{random.randint(10000,99999)}')
        options.add_argument('window-size=1200,900')
        options.add_argument('--lang=en-US,en')
        options.add_argument('--disable-blink-features=AutomationControlled')
        # Do NOT use headless mode
        # --- End anti-bot evasion settings ---
        chrome_version = 135  # Update this to match your Chrome version
        driver = uc.Chrome(version_main=chrome_version, options=options)
        
        driver.get(url)
        time.sleep(random.uniform(2, 4))
        
        # Accept cookies if the dialog appears
        try:
            cookie_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, '//button[contains(text(), "Accept")]'))
            )
            cookie_button.click()
            print("Accepted cookies")
            time.sleep(random.uniform(1, 2))
        except TimeoutException:
            print("No cookie dialog found or it was already accepted")
        
        # Allow some time for the page to fully load
        print("Waiting for page to load completely...")
        time.sleep(5)
        
        # Try to close any overlays/popups that might appear
        close_overlays(driver)
        time.sleep(random.uniform(1, 2))
        
        # Create output directory if it doesn't exist
        output_dir = 'buildcom'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"buildcom_{timestamp}.txt")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Build.com - Product Titles\n")
            f.write(f"# Source: {url}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing product titles: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        product_count = 0
        
        while current_page <= max_pages:
            print(f"\n==== Scraping page {current_page} ====")
            
            # Ensure the page has loaded properly
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'a[data-automation="product-card-description-link"]'))
                )
            except TimeoutException:
                print("Products didn't load within the expected time. Continuing anyway...")
            
            # Scroll down slowly to load all products
            for i in range(10):
                driver.execute_script(f"window.scrollTo(0, {i * 500});")
                time.sleep(random.uniform(0.2, 0.5))
            
            # Get the page source after scrolling to ensure all content is loaded
            html = driver.page_source
            page_results = process_page(html)
            time.sleep(random.uniform(1, 2))
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Use Selenium to click the next pagination button
            try:
                # Scroll to bottom to make pagination visible
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(1)
                
                # Find the next page button
                next_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'div[data-automation="next-page-button"]'))
                )
                
                if next_button:
                    print("Clicking next pagination button...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                    time.sleep(random.uniform(1, 2))
                    
                    try:
                        next_button.click()
                    except ElementClickInterceptedException:
                        print("Direct click failed, trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", next_button)
                    
                    # Wait longer for the new page content to load completely
                    print("Waiting for next page to load...")
                    time.sleep(random.uniform(4, 6))
                    
                    # Check for URL change to ensure we're on a new page
                    try:
                        WebDriverWait(driver, 10).until(
                            lambda d: "product-card-description-link" in d.page_source
                        )
                    except TimeoutException:
                        print("New page products didn't load. Attempting to continue...")
                    
                    # Scroll back to top of page
                    driver.execute_script("window.scrollTo(0, 0);")
                    time.sleep(2)
                    
                    current_page += 1
                else:
                    print("No next pagination button found. Reached the last page.")
                    break
            except TimeoutException:
                print("Next button not found or not clickable. Reached the last page.")
                break
            except Exception as e:
                print(f"Error during pagination: {str(e)}")
                # Try an alternative method using select dropdown if button click fails
                try:
                    print("Trying alternative pagination method...")
                    select_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, 'select[aria-label="page number"]'))
                    )
                    
                    from selenium.webdriver.support.ui import Select
                    select = Select(select_element)
                    next_page_value = str(current_page + 1)
                    
                    # Check if the next page exists in options
                    options = [option.get_attribute('value') for option in select.options]
                    if next_page_value in options:
                        print(f"Selecting page {next_page_value} from dropdown...")
                        select.select_by_value(next_page_value)
                        time.sleep(random.uniform(4, 6))
                        current_page += 1
                    else:
                        print("No more pages available in the pagination dropdown.")
                        break
                except Exception as select_e:
                    print(f"Alternative pagination method also failed: {str(select_e)}")
                    break
                
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All product titles have been saved to: {os.path.abspath(filename)}")
        if all_results:
            print("\nSample Product Titles:")
            for i, title in enumerate(all_results[:10], 1):
                print(f"{i}. {title}")
            if len(all_results) > 10:
                print(f"... and {len(all_results) - 10} more")
            print(f"\nTotal products found: {len(all_results)}")
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        if driver:
            print("Closing browser...")
            driver.quit()

def close_overlays(driver):
    # Try to close various types of overlays common on e-commerce sites
    selectors = [
        '//button[@aria-label="Close"]',
        '//div[@aria-label="Close"]',
        '//button[contains(text(), "Close")]',
        '//button[contains(@class, "close")]',
        '//span[contains(@class, "close")]',
        '//div[contains(@class, "popup-close")]',
        '//button[contains(text(), "No thanks")]',
        '//a[contains(text(), "Close")]'
    ]
    
    for selector in selectors:
        try:
            close_buttons = driver.find_elements(By.XPATH, selector)
            if close_buttons:
                print(f"Found {len(close_buttons)} potential overlay(s) with selector {selector}. Attempting to close...")
                for button in close_buttons:
                    try:
                        button.click()
                        print("Clicked a close button.")
                        time.sleep(random.uniform(0.5, 1))
                    except Exception as e:
                        print(f"Could not click close button: {e}")
                        try:
                            driver.execute_script("arguments[0].click();", button)
                            print("Used JavaScript to click close button.")
                            time.sleep(random.uniform(0.5, 1))
                        except Exception as js_e:
                            print(f"JavaScript click also failed: {js_e}")
        except Exception as e:
            print(f"Error finding overlay close buttons with selector {selector}: {e}")

def process_page(html):
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    
    # Look for products using the specific selector for Build.com
    product_links = soup.find_all('a', {'data-automation': 'product-card-description-link'})
    
    if product_links:
        print(f"Found {len(product_links)} products using the product-card-description-link selector")
        for link in product_links:
            try:
                # Find the title div within the link
                title_div = link.find('div', class_='lh-title')
                
                if title_div:
                    # Extract the full title text
                    full_title = title_div.get_text(strip=True)
                    
                    # Find the brand name which is in a span with fw6 class
                    brand_span = title_div.find('span', class_='fw6')
                    brand_name = brand_span.get_text(strip=True) if brand_span else ""
                    
                    # Remove brand name from beginning of title if present
                    if brand_name and full_title.startswith(brand_name):
                        title_text = full_title[len(brand_name):].strip()
                    else:
                        title_text = full_title
                    
                    # Extract only the product type without dimensions and visual descriptions
                    import re
                    
                    # Step 1: Check if there's an inch symbol (") in the title
                    # If so, extract everything after the LAST inch symbol and before any hyphen
                    if '"' in title_text:
                        # Split by the inch symbol and take everything after the last one
                        parts_after_inch = title_text.split('"')[-1].strip()
                        
                        # Remove any leading dimensions or formatting characters
                        parts_after_inch = re.sub(r'^[\s\dxX\-\'".]+', '', parts_after_inch)
                        
                        # Then split by hyphen and take only the first part
                        product_type = parts_after_inch.split(' - ')[0].strip()
                        
                        # Remove "Sold by" and everything after if it exists
                        if 'Sold by' in product_type:
                            product_type = product_type.split('Sold by')[0].strip()
                        
                        if product_type:
                            results.append(product_type)
                            continue
                    
                    # Step 2: If no inch symbol or nothing after it, try splitting by hyphens
                    parts = title_text.split(' - ')
                    if len(parts) >= 2:
                        # Take the middle part (typically the product description)
                        middle_part = parts[1].strip()
                        
                        # Remove any dimensions from the beginning if they exist
                        clean_part = re.sub(r'^[\s\dxX\-\'".]+', '', middle_part)
                        
                        if clean_part:
                            results.append(clean_part)
                            continue
                    
                    # Step 3: Last resort, try to extract anything that looks like a product type
                    product_patterns = [
                        r'((?:Rectangle|Square|Hexagon|Octagon|Specialty|Random|Linear)\s+.*?(?:Tile|Flooring|Plank))',
                        r'(.*?(?:Tile|Flooring|Plank|Mosaic))',
                        r'(.*?Visual)'
                    ]
                    
                    for pattern in product_patterns:
                        match = re.search(pattern, title_text)
                        if match:
                            product_type = match.group(1).strip()
                            # Remove any "- Visual" or "- Finish" parts
                            product_type = re.sub(r'\s+-\s+.*?(?:Visual|Finish)$', '', product_type)
                            results.append(product_type)
                            break
                    else:
                        # If we get here, no pattern matched, use a simple approach
                        # Just add whatever text we have, removing obvious dimensions
                        clean_text = re.sub(r'^[\s\dxX\-\'".]+|Sold by.*$', '', title_text).strip()
                        results.append(clean_text)
                
            except Exception as e:
                print(f"Error extracting product info: {e}")
        
        # Post-process to clean up the results
        clean_results = []
        for title in results:
            # Remove any remaining dimensions at the beginning
            clean = re.sub(r'^[\s\dxX\-\'".]+', '', title).strip()
            # Remove any "Sold by" or visual descriptions
            clean = re.sub(r'\s+Sold by.*$|\s+-\s+.*?(?:Visual|Finish)$', '', clean).strip()
            # Remove common words like "Matte" or "Glossy" at the end
            clean = re.sub(r'\s+(?:Matte|Glossy|Textured|Polished|Unpolished|Satin|Wood)\s*$', '', clean).strip()
            
            if clean:
                clean_results.append(clean)
        
        return clean_results
    
    # If the primary selector didn't work, try alternative selectors
    print("No products found with the primary selector. Trying alternative selectors...")
    
    # Try other common product title selectors for Build.com
    alternative_selectors = [
        ('div', 'product-title'),
        ('h3', 'product-name'),
        ('div', 'product-card-description'),
        ('div', 'product-name')
    ]
    
    for tag, class_name in alternative_selectors:
        product_titles = soup.find_all(tag, class_=class_name)
        if product_titles:
            print(f"Found {len(product_titles)} products using {tag}.{class_name} selector")
            for title_elem in product_titles:
                try:
                    # Get the product title text and normalize whitespace
                    title_text = title_elem.get_text(strip=True)
                    # Replace multiple spaces and newlines with a single space
                    title = ' '.join(title_text.split())
                    
                    if title:
                        # Try to extract product type using the same approaches as above
                        import re
                        
                        if '"' in title:
                            parts_after_inch = title.split('"')[-1].strip()
                            parts_after_inch = re.sub(r'^[\s\dxX\-\'".]+', '', parts_after_inch)
                            product_type = parts_after_inch.split(' - ')[0].strip()
                            if product_type:
                                results.append(product_type)
                                continue
                        
                        # Try splitting by hyphen if inch approach didn't work
                        parts = title.split(' - ')
                        if len(parts) >= 2:
                            middle_part = parts[1].strip()
                            clean_part = re.sub(r'^[\s\dxX\-\'".]+', '', middle_part)
                            if clean_part:
                                results.append(clean_part)
                                continue
                        
                        # Just use the original title with dimensions removed
                        clean_text = re.sub(r'^[\s\dxX\-\'".]+|Sold by.*$', '', title).strip()
                        results.append(clean_text)
                except Exception as e:
                    print(f"Error extracting product info: {e}")
            break
    
    if not results:
        print("No products found with any of the selectors. The site structure might have changed.")
        print("Dumping some HTML to help debug:")
        # Print a small section of the HTML to help debug
        print(soup.prettify()[:1000])
    
    # Apply post-processing to clean up results
    clean_results = []
    for title in results:
        clean = re.sub(r'^[\s\dxX\-\'".]+', '', title).strip()
        clean = re.sub(r'\s+Sold by.*$|\s+-\s+.*?(?:Visual|Finish)$', '', clean).strip()
        clean = re.sub(r'\s+(?:Matte|Glossy|Textured|Polished|Unpolished|Satin|Wood)\s*$', '', clean).strip()
        
        if clean:
            clean_results.append(clean)
    
    return clean_results

def main():
    print("Build.com Product Crawler")
    custom_url = URL
    max_pages = 10
    
    print(f"\nStarting crawler for: {custom_url}")
    print(f"Will crawl up to {max_pages} pages\n")
    
    # Ensure output directory exists
    os.makedirs('buildcom', exist_ok=True)
    print(f"Build.com directory created/verified at: {os.path.abspath('buildcom')}")
    
    selenium_crawl(custom_url, max_pages=max_pages)

if __name__ == "__main__":
    main() 