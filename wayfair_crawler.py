import time
import os
import datetime
import undetected_chromedriver as uc
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# Set your target URL here
URL = "https://www.wayfair.com/lighting/sb0/ceiling-fans-c1786439.html"

def selenium_crawl(url, max_pages=10):  # Increased default max_pages to 10
    driver = None
    try:
        options = uc.ChromeOptions()
        driver = uc.Chrome(options=options)
        driver.get(url)
        
        input("Please solve the CAPTCHA in the browser, then press Enter here to continue...")
        
        # Create output directory if it doesn't exist
        output_dir = 'wayfair'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"wayfair_gazebos_{timestamp}.txt")
        
        # Create file with minimal header
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Wayfair Gazebos - Keywords\n")
            f.write(f"# Source: {URL}\n")
            f.write(f"# Scraped: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n")
        
        print(f"Created file for storing keywords: {os.path.abspath(filename)}")
        
        all_results = []
        current_page = 1
        last_page_reached = False
        product_count = 0
        
        # Keep crawling until we reach the last page or hit the max_pages limit
        while current_page <= max_pages and not last_page_reached:
            # Process the current page
            print(f"\n==== Scraping page {current_page} ====")
            page_results = process_page(driver.page_source)
            
            if page_results:
                all_results.extend(page_results)
                print(f"Found {len(page_results)} products on page {current_page}")
                
                # Append this page's results to the file immediately
                with open(filename, 'a', encoding='utf-8') as f:
                    for title in page_results:
                        product_count += 1
                        f.write(f"{title}\n")
                
                # Print the first few titles from this page
                for i, title in enumerate(page_results[:5], 1):
                    print(f"  {i}. {title}")
                if len(page_results) > 5:
                    print(f"  ... and {len(page_results) - 5} more")
                
                print(f"  Appended {len(page_results)} products to {filename}")
            else:
                print(f"No products found on page {current_page}")
            
            # Check if there's a next page
            if current_page >= max_pages:
                print(f"Reached maximum page limit ({max_pages})")
                break
            
            # Try to go to the next page
            next_page = current_page + 1
            try:
                # First, check for and close any popups or overlays
                try:
                    close_overlays(driver)
                except Exception as e:
                    print(f"Note: Error handling overlay: {e}")
                
                # Wait for a moment
                time.sleep(2)
                
                # Try to find pagination links
                try:
                    # Look for next page button
                    print(f"Looking for page {next_page} link...")
                    
                    # Try different methods to find the pagination link
                    pagination_link = None
                    
                    # Method 1: aria-label
                    try:
                        pagination_link = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, f'//a[@aria-label="Page {next_page}"]'))
                        )
                        print(f"Found page link using aria-label")
                    except TimeoutException:
                        # Method 2: href containing curpage parameter
                        try:
                            pagination_link = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, f'//a[contains(@href, "curpage={next_page}")]'))
                            )
                            print(f"Found page link using href")
                        except TimeoutException:
                            # Method 3: generic "Next" button
                            try:
                                pagination_link = WebDriverWait(driver, 5).until(
                                    EC.element_to_be_clickable((By.XPATH, '//a[contains(@aria-label, "Next") or contains(text(), "Next")]'))
                                )
                                print(f"Found Next button")
                            except TimeoutException:
                                print("No more pagination links found. Reached the last page.")
                                last_page_reached = True
                                break
                    
                    if pagination_link:
                        print(f"Scrolling to pagination link...")
                        # Scroll to make sure the element is in view
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", pagination_link)
                        time.sleep(2)  # Wait for scroll to complete
                        
                        # Try to click
                        try:
                            print("Attempting to click pagination link...")
                            pagination_link.click()
                        except ElementClickInterceptedException:
                            print("Direct click failed, trying JavaScript click...")
                            driver.execute_script("arguments[0].click();", pagination_link)
                        
                        # Wait for the page to load
                        print("Waiting for page to load...")
                        time.sleep(5)
                        current_page = next_page
                    else:
                        print("No pagination link found. Reached the last page.")
                        last_page_reached = True
                        break
                        
                except Exception as e:
                    print(f"Error navigating to next page: {e}")
                    print("Assuming this is the last page.")
                    last_page_reached = True
                    break
                    
            except Exception as e:
                print(f"Error during pagination: {e}")
                break
        
        # No summary at the end of the file to keep it clean
        # Just log to console
        print("\n==== Crawling complete ====")
        print(f"Scraped {current_page} pages with {len(all_results)} total products")
        print(f"All keywords have been saved to: {os.path.abspath(filename)}")
            
        # Show all results
        if all_results:
            print("\nAll Product Titles:")
            for i, title in enumerate(all_results, 1):
                print(f"{i}. {title}")
            print(f"\nTotal products found: {len(all_results)}")
        
        # Keep browser open until user decides to close
        input("\nPress Enter to close the browser and finish the script...")
        
        return all_results
    except WebDriverException as e:
        print(f"Selenium error: {e}")
        return []
    finally:
        # Make sure we don't leave the browser open if there's an error
        # but only if the user hasn't chosen to keep it open
        if driver and input("Do you want to keep the browser open? (y/n): ").lower() != 'y':
            driver.quit()

def close_overlays(driver):
    """Try to close any popups or overlays that might block pagination"""
    # Check for elements with "Close" aria-label
    close_buttons = driver.find_elements(By.XPATH, '//div[@aria-label="Close"]')
    if close_buttons:
        print(f"Found {len(close_buttons)} potential overlay(s). Attempting to close...")
        for button in close_buttons:
            try:
                button.click()
                print("Clicked a close button.")
                time.sleep(1)  # Wait a moment after closing
            except Exception as e:
                print(f"Could not click close button: {e}")
                try:
                    # Try JavaScript click if regular click fails
                    driver.execute_script("arguments[0].click();", button)
                    print("Used JavaScript to click close button.")
                    time.sleep(1)
                except Exception as js_e:
                    print(f"JavaScript click also failed: {js_e}")

def process_page(html):
    """Process a single page and extract product titles"""
    results = []
    soup = BeautifulSoup(html, 'html.parser')
    
    # Find all h2 elements with the specified attributes and classes
    product_titles = soup.find_all('h2', {
        'data-hb-id': 'Text', 
        'data-test-id': 'ListingCard-ListingCardName-Text',
        'data-name-id': 'ListingCardName',
        'class': lambda x: x and ('_6o3atzbl' in x) and ('_6o3atz174' in x) and ('_1lxwj2q1' in x)
    })
    
    if not product_titles:
        print("No product titles found with specific selector.")
        # Try alternative selectors if needed
        product_titles = soup.find_all('h2')
        if product_titles:
            print(f"Found {len(product_titles)} h2 elements using broader selector.")
    
    for title in product_titles:
        title_text = title.get_text(strip=True)
        results.append(title_text)
    
    return results

def main():
    selenium_crawl(URL, max_pages=20)  # Increased to 20 pages max

if __name__ == "__main__":
    main() 